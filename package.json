{"name": "new_gao<PERSON><PERSON>_abroad_nuxt", "private": true, "type": "module", "scripts": {"start": "nuxt dev", "test": "cross-env NODE_ENV_PROXY=test nuxt dev", "dev": "cross-env NODE_ENV_PROXY=dev nuxt dev", "pre": "cross-env NODE_ENV_PROXY=pre nuxt dev", "gray": "cross-env NODE_ENV_PROXY=gray nuxt dev", "dong": "cross-env NODE_ENV_PROXY=dong nuxt dev", "build": "nuxt build", "build:test": "cross-env NODE_ENV_PROXY=test nuxt build", "build:dev": "cross-env NODE_ENV_PROXY=dev nuxt build", "build:pre": "cross-env NODE_ENV_PROXY=pre nuxt build", "build:gray": "cross-env NODE_ENV_PROXY=gray nuxt build", "build:dong": "cross-env NODE_ENV_PROXY=dong nuxt build", "serve": "rm -rf dist && cp -r .output dist", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.7.6", "mitt": "^3.0.1", "nuxt": "^3.12.2", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@element-plus/nuxt": "^1.0.9", "@types/mockjs": "^1.0.10", "cross-env": "^7.0.3", "mockjs": "^1.1.0", "prettier": "^3.3.2", "sass": "^1.77.6"}}