import mitt, { type Emitter } from 'mitt'

const eventBus: Emitter<{
  scroll: void
  resize: void
}> = mitt()

export default function useConfig() {
  const runtimeConfig = useRuntimeConfig()
  const { apiBase, urlBase, env } = runtimeConfig.public

  const title = '高才海外'
  const domain = 'gaoxiaojob.com'
  const domainUrl = `//www.${domain}`
  const qqNumber = '2881224205'
  const phoneNumber = '020-85611139'
  const mobileNumber = '15920573323'
  const emailAddress = '<EMAIL>'

  const personUrl = `${urlBase}/member/person`
  const companyUrl = `${urlBase}/member/company`
  const personLoginUrl = `${personUrl}/login`
  const companyCooperationUrl = `${companyUrl}/applyCooperation`
  const imagePrefix = `https://img.gaoxiaojob.com/uploads/haiwai`

  const routeList = {
    home: '/',
    talent: '/chuhai',
    activity: '/guiguo',
    announcement: '/gonggao',
    youth: '/haiwaiyouqing',
    company: '/danwei',
    about: '/gaocai',
    aboutService: '/gaocai#service',
    aboutAdvantage: '/gaocai#advantage',
    aboutCase: '/gaocai#case',
  }

  const isRelease = env === 'release'

  return {
    apiBase,
    urlBase,
    env,
    isRelease,
    eventBus,
    routeList,

    title,
    domain,
    domainUrl,
    qqNumber,
    phoneNumber,
    mobileNumber,
    emailAddress,

    personUrl,
    companyUrl,
    personLoginUrl,
    companyCooperationUrl,
  }
}
