namespace Announcement {
  interface Data {
    announcementList: AnnouncementList
    announcementSearchList: AnnouncementSearchList
    banner: API.Banner[]
    bottomShowcaseList: BottomShowcaseList[]
    footerShowcase: FooterShowcase
    headlinesList: HeadlinesList
    hotAnnouncementList: HotAnnouncementList[]
    hotJobList: HotJobList[]
    jobList: JobList
    jobSearchList: JobSearchList
    topShowcaseList: TopShowcaseList[]
    [property: string]: any
  }

  interface AnnouncementList {
    count: number
    limit: number
    list: AnnouncementListList[]
    page: number
    [property: string]: any
  }

  interface AnnouncementListList {
    address?: string
    attributeType?: string
    highlightsDescribe?: string
    id?: string
    jobAmount?: string
    recruitAmount?: string
    refreshDate?: string
    refreshYear?: string
    tag?: string
    title?: string
    url?: string
    [property: string]: any
  }

  interface AnnouncementSearchList {
    areaId: AreaId[]
    cityId: AnnouncementSearchListCityId[]
    companyType: AnnouncementSearchListCompanyType[]
    jobCategoryId: AnnouncementSearchListJobCategoryId[]
    majorId: AnnouncementSearchListMajorId[]
    refreshType: AnnouncementSearchListRefreshType[]
    [property: string]: any
  }

  interface AreaId {
    k?: number
    v?: string
    [property: string]: any
  }

  interface AnnouncementSearchListCityId {
    k: string
    v: string
    [property: string]: any
  }

  interface AnnouncementSearchListCompanyType {
    k: string
    v: string
    [property: string]: any
  }

  interface AnnouncementSearchListJobCategoryId {
    k: string
    v: string
    [property: string]: any
  }

  interface AnnouncementSearchListMajorId {
    k: string
    v: string
    [property: string]: any
  }

  interface AnnouncementSearchListRefreshType {
    k?: string
    v?: string
    [property: string]: any
  }

  interface Banner {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface BottomShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface FooterShowcase {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface HeadlinesList {
    otherLines: OtherLine[]
    topList: TopList
    [property: string]: any
  }

  interface OtherLine {
    attributeType?: string
    id?: string
    refreshDate?: string
    title?: string
    url?: string
    [property: string]: any
  }

  interface TopList {
    attributeType: string
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  interface HotAnnouncementList {
    address?: string
    articleId?: string
    clickAmount?: string
    companyName?: null
    highlightsDescribe?: string
    id?: string
    jobAmount?: string
    major?: string
    recruitAmount?: string
    refreshDate?: string
    title?: string
    url?: string
    [property: string]: any
  }

  interface HotJobList {
    address?: string
    announcementName?: string
    clickAmount?: string
    companyId?: string
    companyName?: string
    id?: string
    major?: string
    recruitAmount?: string
    refreshDate?: string
    url?: string
    wage?: string
    [property: string]: any
  }

  interface JobList {
    count: number
    limit: number
    list: JobListList[]
    page: number
    [property: string]: any
  }

  interface JobListList {
    address?: string
    amount?: string
    cityId?: string
    companyId?: string
    companyLogo?: string
    companyName?: string
    id?: string
    jobName?: string
    major?: string
    majorId?: string
    maxWage?: string
    minWage?: string
    recruitAmount?: string
    refreshDate?: string
    refreshTime?: string
    url?: string
    wage?: string
    wageType?: string
    [property: string]: any
  }

  interface JobSearchList {
    cityId: JobSearchListCityId[]
    companyType: JobSearchListCompanyType[]
    jobCategoryId: JobSearchListJobCategoryId[]
    majorId: JobSearchListMajorId[]
    refreshType: JobSearchListRefreshType[]
    [property: string]: any
  }

  interface JobSearchListCityId {
    k: string
    v: string
    [property: string]: any
  }

  interface JobSearchListCompanyType {
    k: string
    v: string
    [property: string]: any
  }

  interface JobSearchListJobCategoryId {
    k: string
    v: string
    [property: string]: any
  }

  interface JobSearchListMajorId {
    k: string
    v: string
    [property: string]: any
  }

  interface JobSearchListRefreshType {
    k?: string
    v?: string
    [property: string]: any
  }

  interface TopShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface Request {
    /**
     * 职位类型
     */
    categoryId?: string
    /**
     * 城市
     */
    cityId?: string
    /**
     * 单位类型
     */
    companyType?: string
    /**
     * 专业
     */
    majorId?: string
    /**
     * 发布时间类型
     */
    refreshType?: string
    [property: string]: any
  }
}
