namespace Youth {
  export interface Response {
    data: Data
    msg: string
    result: number
    [property: string]: any
  }

  export interface Data {
    banner: API.Banner[]
    cooperationCaseList: CooperationCaseList[]
    headlinesList: HeadlinesList
    hotAnnouncement: HotAnnouncement[]
    hotCompany: HotCompany[]
    lastActivityList: LastActivityList[]
    moreActivityList: MoreActivityList
    projectInfo: ProjectInfo
    recommendAnnouncementList: RecommendAnnouncementList
    relateNewsList: Array<RelateNewsList[]>
    relyAnnouncementAreaList: RelyAnnouncementAreaList[]
    relyAnnouncementList: RelyAnnouncementList
    relyCompanyList: RelyCompanyList[]
    [property: string]: any
  }

  export interface CooperationCaseList {
    image: string
    otherImageUrl: string
    secondTitle: string
    subTitle: string
    title: string
    url: string
    [property: string]: any
  }

  export interface HeadlinesList {
    otherLines: OtherLine[]
    topList: HeadlinesListTopList
    [property: string]: any
  }

  export interface OtherLine {
    attributeType: string
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface HeadlinesListTopList {
    attributeType: string
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface HotAnnouncement {
    articleId: string
    clickAmount: string
    id: string
    status: string
    title: string
    url: string
    refreshDate: string
    [property: string]: any
  }

  export interface HotCompany {
    city: null
    cityId: string
    companyName: string
    id: string
    info: string
    logo: string
    logoUrl: string
    type: string
    url: string
    [property: string]: any
  }

  export interface LastActivityList {
    image: string
    id: string
    number: string
    title: string
    url: string
    [property: string]: any
  }

  export interface MoreActivityList {
    image: string
    title: string
    url: string
    id: string
    number: string
    [property: string]: any
  }

  export interface ProjectInfo {
    description: string
    dryList: DryList[]
    trendsList: TrendsList[]
    [property: string]: any
  }

  export interface DryList {
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface TrendsList {
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface RecommendAnnouncementList {
    bottomList: BottomList[]
    topList: TopListElement[]
    [property: string]: any
  }

  export interface BottomList extends API.Rel {
    companyName: string
    image: string
    title: string
    url: string
    [property: string]: any
  }

  export interface TopListElement {
    companyLogo: string
    companyName: string
    image: string
    title: string
    url: string
    [property: string]: any
  }

  export interface RelateNewsList {
    id: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface RelyAnnouncementAreaList {
    k: number
    v: string
    [property: string]: any
  }

  export interface RelyAnnouncementList {
    count: number
    limit: number
    list: List[]
    page: number
    [property: string]: any
  }

  export interface List extends API.Rel {
    address: string
    btnText: string
    companyId: string
    companyLogo: string
    companyName: string
    highlightsDescribe: string
    id: string
    date: string
    refreshDate: string
    title: string
    url: string
    [property: string]: any
  }

  export interface RelyCompanyList {
    id: string
    number: string
    image: string
    subTitle: string
    title: string
    url: string
    [property: string]: any
  }

  interface Request {
    areaType: number
    page: number
  }
}
