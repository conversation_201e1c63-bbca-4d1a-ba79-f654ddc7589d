namespace API {
  type AuthData = { avatar: string }

  interface CommonTDK {
    title: string
    keywords: string
    description: string
  }

  interface ShowcaseToken {
    token: string
  }

  interface Rel {
    rel: '' | 'nofollow'
  }

  interface Banner extends Rel {
    image: string
    url: string
    number:string
    id:string
  }

  interface About {
    banner: Banner[]
    cooperationCaseList: Youth.CooperationCaseList
    headerData: Base.Any
    serviceData: Base.Any
  }

  interface OverseasExcellentYouth extends API.Rel {
    address: string
    btnText: string
    companyLogo: string
    companyName: string
    date: string
    highlightsDescribe: string
    id: string
    refreshDate: string
    title: string
    url: string
  }

  interface JobList {
    address?: string
    companyLogo?: string
    companyName?: string
    id?: string
    jobName?: string
    major?: string
    recruitAmount?: string
    refreshDate?: string
    url?: string
    wage?: string
  }

  interface AnnouncementList {
    address?: string
    highlightsDescribe?: string
    id?: string
    jobAmount?: string
    recruitAmount?: string
    refreshDate?: string
    refreshYear?: string
    tag?: string
    title?: string
    url?: string
  }

  interface CaseList extends Rel {
    image: string
    url: string
    title: string
    [property: string]: any
  }

  interface AdvertiseData {
    topShowcaseList: CaseList[]
    bottomShowcaseList: CaseList[]
  }
}
