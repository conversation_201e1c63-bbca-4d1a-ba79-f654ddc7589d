namespace Company {
  export interface Request {
    areaId: string
    companyType: string
    page: number
  }

  export interface CompanyData {
    /**
     * 城市列表
     */
    areaList: AreaList[]
    /**
     * 顶部banner
     */
    banner: API.Banner[]
    /**
     * 广告位-下4
     */
    bottomShowcaseList: BottomShowcaseList[]
    /**
     * 单位列表
     */
    companyList: CompanyList
    /**
     * 单位类型列表
     */
    companyTypeList: CompanyTypeList[]
    /**
     * 广告位-上2
     */
    topShowcaseList: TopShowcaseList[]
    [property: string]: any
  }

  interface CompanyList {
    count: number
    limit: number
    list: List[]
    page: number
    [property: string]: any
  }

  export interface AreaList {
    id: number | string
    label: string
    name: string
    value: number | string
    [property: string]: any
  }

  export interface BottomShowcaseList extends API.Rel {
    image: string
    url: string
    title: string
    [property: string]: any
  }

  export interface List {
    address: string
    announcementList: AnnouncementList[]
    announcementUrl: string
    areaId: string
    companyInfo: string
    companyUrl: string
    id: string
    logoUrl: string
    name: string
    nature: string
    onlineAnnouncementAmount: string
    onlineJobAmount: string
    type: string
    url: string
    [property: string]: any
  }

  export interface AnnouncementList {
    announcementUrl: string
    id: string
    refreshDate: string
    title: string
    [property: string]: any
  }

  export interface CompanyTypeList {
    k: string
    v: string
    [property: string]: any
  }
}
