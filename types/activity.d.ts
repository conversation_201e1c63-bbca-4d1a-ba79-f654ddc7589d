namespace Activity {
  interface Data {
    activityList: ActivityList
    banner: API.Banner[]
    bottomShowcaseList: BottomShowcaseList[]
    moreActivityList: Array<MoreActivityList[]>
    newsList: NewsList
    searchParams: SearchParams
    topShowcaseList: TopShowcaseList[]
    [property: string]: any
  }

  interface ActivityList {
    count: number
    limit: number
    list: List[]
    page: number
    [property: string]: any
  }

  interface List {
    activityChildStatus?: string
    activityEndDate?: string
    activityStartDate?: string
    activityStatus?: string
    address?: string
    btnDisabled?: boolean
    btnText?: null
    date?: string
    description?: string
    id?: string
    image?: string
    imageInfo?: ImageInfo
    isOutsideUrl?: string
    logo?: string
    logoFileId?: string
    mainImgFileId?: string
    name?: string
    rel?: string
    reviewImgFileIds?: string
    sessionList?: SessionList[]
    signEndDate?: string
    tag?: string
    type?: string
    url?: string
    [property: string]: any
  }

  interface ImageInfo {
    isMain: number
    list: string[]
    tag: string
    [property: string]: any
  }

  interface SessionList {
    activityAddress: string
    activityDate: string
    name: string
    [property: string]: any
  }

  interface Banner {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface BottomShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface MoreActivityList {
    address?: string
    highlightsDescribe?: string
    id?: string
    periodDate?: string
    refreshDate?: string
    refreshYear?: string
    title?: string
    url?: string
    [property: string]: any
  }

  interface NewsList {
    pageList: Array<PageList[]>
    topNews: TopNews
    [property: string]: any
  }

  interface PageList {
    coverThumb?: string
    id?: string
    isTop?: boolean
    refreshDate?: string
    seoDescription?: string
    title?: string
    [property: string]: any
  }

  interface TopNews {
    coverThumb: string
    id: string
    isTop: boolean
    refreshDate: string
    seoDescription: string
    title: string
    [property: string]: any
  }

  interface SearchParams {
    activityStatus: ActivityStatus[]
    activityTime: ActivityTime[]
    activityType: ActivityType[]
    area: Area[]
    companyCategory: CompanyCategory[]
    [property: string]: any
  }

  interface ActivityStatus {
    active: boolean
    id: number
    name: string
    url: string
    [property: string]: any
  }

  interface ActivityTime {
    active: boolean
    id: number
    name: string
    url: string
    [property: string]: any
  }

  interface ActivityType {
    active: boolean
    id: number
    key: string
    name: string
    url: string
    [property: string]: any
  }

  interface Area {
    active: boolean
    id: number | string
    key: string
    level: string
    name: string
    url: string
    [property: string]: any
  }

  interface CompanyCategory {
    active: boolean
    id: number | string
    name: string
    url: string
    [property: string]: any
  }

  interface TopShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }
}
