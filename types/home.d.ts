namespace Home {
  interface CollaborationCase extends API.Rel {
    description: string
    id: string
    id: number
    url: string
    logoImage: string
    otherImage: string
    title: string
  }

  interface BottomList extends API.Rel {
    address: string
    btnDisabled: boolean
    btnText: string
    date: string
    description: string
    id: string
    image: string
    logo: string
    name: string
    tag: string
    title: string
    type: string
    url: string
    signUpUrl: string
  }

  interface TopList extends API.Rel {
    address: string
    btnDisabled: boolean
    btnText: string
    date: string
    description: string
    id: string
    image: string
    logo: string
    name: string
    tag: string
    title: string
    type: string
    url: string
    signUpUrl: string
  }

  interface ComeBackActivities {
    bottomList: BottomList[]
    topList: TopList[]
  }

  interface DiscoveryPosition {
    address: string
    companyLogo: string
    companyName: string
    id: string
    jobName: string
    major: string
    recruitAmount: string
    refreshDate: string
    url: string
    wage: string
  }

  interface GlobalAnnouncement {
    address: string
    highlightsDescribe: string
    id: string
    jobAmount: string
    recruitAmount: string
    refreshDate: string
    refreshYear: string
    tag: string
    title: string
    url: string
  }

  interface OverseasTalentAttractionActivity extends API.Rel {
    address: string
    btnDisabled: boolean
    btnText: string
    date: string
    id: string
    image: string
    logo: string
    tag: string
    title: string
    type: string
    url: string
    signUpUrl: string
  }

  interface RecommendedActivity extends API.Rel {
    address: string
    date: string
    description: string
    id: string
    image: string
    logo: string
    name: string
    tag: string
    title: string
    type: string
    url: string
    otherDescriptionOne: string
  }

  interface RenownedEmployer extends API.Rel {
    companyInfo: string
    companyLogo: string
    companyName: string
    description: string
    id: string
    image: string
    url: string
    title: string
    number: string
  }

  interface Data {
    banner: API.Banner[]
    /**
     * 合作案例
     */
    collaborationCases: CollaborationCase[]
    /**
     * 归国活动
     */
    comeBackActivities: ComeBackActivities
    /**
     * 发现职位
     */
    discoveryPosition: DiscoveryPosition[]
    /**
     * 全球求贤
     */
    globalAnnouncement: GlobalAnnouncement[]
    /**
     * 海外优青
     */
    overseasExcellentYouth: API.OverseasExcellentYouth[]
    /**
     * 出海引才
     */
    overseasTalentAttractionActivities: OverseasTalentAttractionActivity[]
    /**
     * 推荐活动
     */
    recommendedActivities: RecommendedActivity[]
    /**
     * 知名雇主
     */
    renownedEmployer: RenownedEmployer[]
  }
}
