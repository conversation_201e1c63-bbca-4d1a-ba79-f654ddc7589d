namespace Talent {
  interface Data {
    activityList: ActivityList
    banner: API.Banner[]
    bottomShowcaseList: API.CaseList[]
    moreActivityList: Array<MoreActivityList[]>
    newsList: NewsList
    searchParams: SearchParams
    topShowcaseList: API.CaseList[]
    [property: string]: any
  }

  interface ActivityList {
    count: number
    limit: number
    list: List[]
    page: number
    [property: string]: any
  }

  interface List {
    activityChildStatus?: string
    activityEndDate?: string
    activityStartDate?: string
    activityStatus?: string
    address?: string
    btnDisabled?: boolean
    btnText?: null
    date?: string
    description?: string
    id?: string
    image?: string
    isOutsideUrl?: string
    logo?: string
    logoFileId?: string
    mainImgFileId?: string
    name?: string
    rel?: string
    reviewImgFileIds?: string
    sessionList?: SessionList[]
    signEndDate?: string
    tag?: string
    type?: string
    url?: string
    [property: string]: any
  }

  interface SessionList {
    activityAddress: string
    activityDate: string
    name: string
    [property: string]: any
  }

  interface BottomShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }

  interface MoreActivityList {
    activityEndDate?: string
    activityStartDate?: string
    address?: string
    addTime?: string
    date?: string
    id?: string
    isOutsideUrl?: string
    name?: string
    refreshDate?: string
    refreshYear?: string
    rel?: string
    url?: string
    [property: string]: any
  }

  interface NewsList {
    pageList: Array<PageList[]>
    topNews: TopNews
    [property: string]: any
  }

  interface PageList {
    coverThumb?: string
    id?: string
    isTop?: boolean
    refreshDate?: string
    seoDescription?: string
    title?: string
    [property: string]: any
  }

  interface TopNews {
    coverThumb: string
    id: string
    isTop: boolean
    refreshDate: string
    seoDescription: string
    title: string
    [property: string]: any
  }

  interface SearchParams {
    activityStatus: ActivityStatus[]
    activityTime: ActivityTime[]
    activityType: ActivityType[]
    area: Area[]
    [property: string]: any
  }

  interface ActivityStatus {
    active: boolean
    id: number
    name: string
    url: string
    [property: string]: any
  }

  interface ActivityTime {
    active: boolean
    id: number
    name: string
    url: string
    [property: string]: any
  }

  interface ActivityType {
    active: boolean
    id: number
    key: string
    name: string
    url: string
    [property: string]: any
  }

  interface Area {
    active: boolean
    children: Child[]
    id: number | string
    key: string
    level: string
    name: string
    url: string
    [property: string]: any
  }

  interface Child {
    active: boolean
    id: string
    key: string
    level: string
    name: string
    url: string
    [property: string]: any
  }

  interface TopShowcaseList {
    id: string
    image: string
    number: string
    url: string
    [property: string]: any
  }
}
