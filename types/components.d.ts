namespace Components {
  type BaseTagPropRadius = PropType<'left' | 'right'>

  type BaseTagPropType = PropType<'primary' | 'default' | 'warning'>

  type BaseTagPropSize = PropType<'large' | 'default' | 'small'>

  type BaseLinkPropTarget = PropType<Base.Target>

  type BaseLinkPropSize = PropType<'large' | 'default' | 'small'>

  type BaseMenuPropData = PropType<Base.Menu[]>

  type BaseBannerPropData = PropType<API.Banner[]>

  type BaseSelectOptionsProp = PropType<API.OptionList[]>

  type BaseLinkPropRel = PropType<API.Rel['rel']>

  type BaseYouthListPropData = PropType<API.OverseasExcellentYouth[]>

  type HomeRecommendPropData = PropType<Home.RecommendedActivity[]>

  type HomeTalentPropData = PropType<Home.OverseasTalentAttractionActivity[]>

  type HomeActivityPropData = PropType<Home.ComeBackActivities>

  type HomeCompanyPropData = PropType<Home.RenownedEmployer[]>

  type HomeCasePropData = PropType<Home.CollaborationCase[]>

  type CompanyCardPropData = PropType<Company.List>

  type CompanyAreaData = PropType<Company.CompanyData.areaList>

  type CompanyTypeData = PropType<Company.CompanyData.companyTypeList>

  type BaseGlobalActivityType = PropType<'abroad' | 'native'>

  type AnnouncementJobType = PropType<'announcement' | 'job'>

  type AnnouncementPropData = PropType<API.AnnouncementListData[]>

  type BaseAnnouncementProp = PropType<API.AnnouncementListData>

  type JobPropData = PropType<API.JobListData[]>

  type BaseJobProp = PropType<API.JobListData>

  type ActivityFilterProp = PropType<Announcement.SearchParams>

  type BaseGlobalActivityProp = PropType<Announcement.ActivityList>

  type TalentSearchParamsProp = PropType<Talent.SearchParams>

  type BaseMoreActivityProp = PropType<Activity.MoreActivityList>

  type BaseNewsActivityProp = PropType<Activity.NewsList>

  type AnnouncementFilterProp = PropType<Announcement.jobSearchList>

  type AnnouncementHeavyProp = PropType<Announcement.HeadlinesList>

  type BaseHotAnnouncementProp = PropType<Announcement.HotAnnouncementList>

  type BaseHotJobProp = PropType<Announcement.HotJobList>

  type YouthNoticeProp = PropType<Youth.BottomList>

  type YouthIntroduction = PropType<Youth.DryList[]>

  type BaseCollaboration = PropType<Youth.CooperationCaseList>

  type BaseAdvertise = PropType<Company.AdvertiseData>
}
