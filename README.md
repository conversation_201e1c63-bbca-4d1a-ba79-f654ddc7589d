# new_gaoxiao_abroad_nuxt

## Get Started

### setup

```bash
# npm
npm install
```

### development

```bash
# release
npm start

# test
npm run test

# dev
npm run dev

# pre
npm run pre

# gray
npm run gray
```

### alias

```json
{
  "~": "/<srcDir>",
  "assets": "/<srcDir>/assets",
  "public": "/<srcDir>/public"
}
```

## 开发规范

### 文件和目录命名规范

文件和目录命名，均只能包含小写字母和 `-` 字符

### HTML

页面标签只能包含小写字母和 `-` 字符

### CSS 命名规范

CSS 的命名，采用 BEM 命名规范，可以有效的避免组件间样式的相互污染，减少嵌套层级。

BEM 使用分隔符将类名区隔成三个部分：

[block]\_\_[element]--[modifier]

- block(块) ：组件的最外层父元素，这个类包含最通用和可重用的功能。
- element(元素) ：组件内可包含一个或多个元素，元素为块添加了新功能。无需重置任何属性。
- modifier(修饰类) ：块或元素都可以通过修饰词来表示为变体。

### 提交消息格式

git 提交消息有非常精确的规则。这会产生更易读的消息，在查看项目历史记录时很容易跟踪。

`<type>(<scope>): <subject>`

`commit` 必须包含 `<type>` 和 `<subject>`，`(<scope>)` 可选

`type` 必须是以下之一：

- feat: 新功能
- fix: 错误修复
- docs: 仅更改文档
- style: 不影响代码含义的更改（空格、格式、缺少分号等）
- refactor: 既不修复错误也不添加功能的代码更改
- perf: 提高性能的代码更改
- test: 添加缺失的或纠正现有的测试
- chore: 对构建过程或辅助工具和库（例如文档生成）的更改
