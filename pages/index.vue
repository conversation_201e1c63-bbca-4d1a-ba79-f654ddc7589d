<template>
  <div class="home__container">
    <app-menu :data="menuList" />

    <base-banner :data="banner" />

    <div
      v-if="hasData(recommendedActivities)"
      id="推荐活动"
      class="home__content"
    >
      <base-title
        prefix="推荐"
        suffix="活动"
        english="Recommended Activities"
      />

      <div class="view--width" style="padding-bottom: 60px">
        <home-recommend :data="recommendedActivities" />
      </div>
    </div>

    <div
      v-if="hasData(overseasTalentAttractionActivities)"
      id="出海引才"
      class="home__content background-color--primary"
    >
      <base-title
        prefix="出海"
        suffix="引才"
        english="Overseas Talent Attraction Activities"
        description="海外专场/组团招聘/海外活动"
      />

      <div class="view--width">
        <home-talent :data="overseasTalentAttractionActivities" />

        <div class="home__to--list text-align--center">
          <base-link target="_self" width="140px" size="large" :href="talent" />
        </div>
      </div>
    </div>

    <div
      v-if="
        hasData(comeBackActivities.topList) ||
        hasData(comeBackActivities.bottomList)
      "
      id="归国活动"
      class="home__content"
    >
      <base-title
        prefix="归国"
        suffix="活动"
        english="Homecoming Activities"
        description="学者论坛/学子归国行/创业大赛/人才大会"
      />

      <div class="view--width">
        <home-activity :data="comeBackActivities" />

        <div class="home__to--list text-align--center">
          <base-link
            target="_self"
            style="margin-top: 40px"
            width="140px"
            size="large"
            :href="activity"
          />
        </div>
      </div>
    </div>

    <div
      v-if="hasData(overseasExcellentYouth)"
      :id="`海外优青${youthYear}`"
      class="home__content background-color--primary"
    >
      <base-title
        reverse
        prefix="海外优青"
        :suffix="youthYear"
        english="Overseas Excellent Youth"
        :description="`诚邀全球英才依托申报${youthYear}海外优青项目`"
      />

      <div class="view--width">
        <base-youth-list :data="overseasExcellentYouth" />

        <div class="home__to--list text-align--center">
          <base-link target="_self" width="140px" size="large" :href="youth" />
        </div>
      </div>
    </div>

    <div v-if="hasData(renownedEmployer)" id="知名雇主" class="home__content">
      <base-title
        reverse
        prefix="知名"
        suffix="雇主"
        english="Renowned Employer"
      />

      <div class="view--width">
        <home-company :data="renownedEmployer" />

        <div class="home__to--list text-align--center">
          <base-link
            target="_self"
            style="margin-top: 40px"
            width="140px"
            size="large"
            :href="company"
          />
        </div>
      </div>
    </div>

    <div
      v-if="hasData(globalAnnouncement)"
      id="全球求贤"
      class="home__content background-color--primary"
    >
      <base-title
        reverse
        prefix="全球"
        suffix="求贤"
        english="Global Announcement"
        description="面向海内外招聘引进高层次人才公告发布"
      />

      <div class="view--width">
        <announcement-result :data="globalAnnouncement" />

        <div class="home__to--list text-align--center">
          <base-link
            target="_self"
            width="140px"
            size="large"
            :href="announcement"
          />
        </div>
      </div>
    </div>

    <div v-if="hasData(discoveryPosition)" id="发现职位" class="home__content">
      <base-title
        reverse
        prefix="发现"
        suffix="职位"
        english="Discovering Positions"
      />

      <div class="view--width">
        <announcement-job :data="discoveryPosition" />

        <div class="home__to--list text-align--center">
          <base-link
            target="_self"
            width="140px"
            size="large"
            :href="`${announcement}#英才职位推荐`"
          />
        </div>
      </div>
    </div>

    <div id="服务介绍" class="home__content">
      <base-title prefix="服务" suffix="介绍" english="Service Introduction" />

      <div class="home__service">
        <div class="view--width">
          <home-service />
        </div>
      </div>
    </div>

    <div v-if="hasData(collaborationCases)" class="home__content">
      <base-title
        reverse
        prefix="合作"
        suffix="案例"
        english="Collaboration Cases"
      />

      <div class="view--width">
        <div class="home__to--list text-align--center">
          <home-case :data="collaborationCases" />

          <base-link
            target="_self"
            style="margin-top: 40px"
            width="140px"
            size="large"
            text="更多精选案例"
            :href="aboutCase"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { routeList, env } = useConfig()

// 如果环境不是正式环境，则更改tdk title keywords和description
if (env !== 'release') {
  useHead({
    title: '测试页面首页',
    meta: [
      { name: 'keywords', content: '' },
      { name: 'description', content: '' },
    ],
  })
}

const { currentDate } = useServer()

const youthYear = currentDate.youth

const { talent, activity, youth, company, announcement, aboutCase } = routeList

const {
  banner,
  recommendedActivities,
  overseasTalentAttractionActivities,
  comeBackActivities,
  overseasExcellentYouth,
  renownedEmployer,
  globalAnnouncement,
  discoveryPosition,
  collaborationCases,
} = (await request({ url: '/api/home' })) as Home.Data

const menuList = computed(() => [
  { name: '推荐活动', visible: hasData(recommendedActivities) },
  { name: '出海引才', visible: hasData(overseasTalentAttractionActivities) },
  {
    name: '归国活动',
    visible:
      hasData(comeBackActivities.topList) ||
      hasData(comeBackActivities.bottomList),
  },
  {
    name: `海外优青${youthYear}`,
    visible: hasData(overseasExcellentYouth),
  },
  { name: '知名雇主', visible: hasData(renownedEmployer) },
  { name: '全球求贤', visible: hasData(globalAnnouncement) },
  { name: '发现职位', visible: hasData(discoveryPosition) },
  { name: '服务介绍', visible: true },
])
</script>

<style scoped lang="scss">
.home {
  &__to {
    padding: 40px 0 60px;

    &--list {
      padding: 20px 0 60px;
    }
  }

  &__service {
    height: 517px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
      no-repeat center / 1920px 517px;
  }
}
</style>
