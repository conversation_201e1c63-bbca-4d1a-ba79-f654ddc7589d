<template>
  <div class="activity__container">
    <app-menu :data="menuList" />

    <base-banner :data="banner" class="banner__wrapper" />

    <div class="company__wrapper background-color--primary">
      <div class="view--width">
        <base-advertise-list :data="{ bottomShowcaseList, topShowcaseList }" />
      </div>
    </div>

    <div id="归国活动" class="come__activity">
      <div class="view--width">
        <base-title prefix="归国" suffix="活动" english="Homecoming Activities">
          <template #prefix-icon>
            <div class="activity--icon"></div>
          </template>
        </base-title>

        <activity-filter
          :data="params"
          :value="{ date }"
          @change="handleChange"
        />

        <template v-if="hasData(activityList)">
          <div class="abroad-activity__content">
            <base-global-activity :data="item" v-for="item in activityList" />
          </div>

          <div class="flex--row flex-justify--center">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="activityListCount"
              :default-page-size="activityPageSize"
              :current-page="currentPage"
              @current-change="currentPageChange"
            />
          </div>
        </template>

        <template v-else>
          <div
            class="empty-container flex--column flex--center background-color--default border-radius"
          >
            <base-empty />
          </div>
        </template>
      </div>
    </div>

    <div
      id="更多活动展示"
      v-if="hasData(moreActivityList)"
      class="more__activity"
    >
      <div class="view--width">
        <base-title
          prefix="更多活动"
          suffix="展示"
          english="More Event Displays"
        />

        <talent-activity :data="moreActivityList" />
      </div>
    </div>

    <div
      id="活动动态速递"
      v-if="hasData(newsList.topNews) || hasData(newsList.pageList)"
      class="news__activity background-color--primary"
    >
      <div class="view--width">
        <base-title
          prefix="活动动态"
          suffix="速递"
          english="Activity Dynamic Express"
        />

        <base-news-activity :data="newsList" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Filter {
  date: String[]
}

const activityList = ref([])
const activityListCount = ref(0)
const activityPageSize = ref(0)
const currentPage = ref(1)

const route = useRoute()
const {
  query,
  query: { activityCustomTime, page = 1 },
  params: { path },
} = route

currentPage.value = Number(page)
const apiSuffix = Array.isArray(path) ? path.join('/') : 'list'

const date =
  activityCustomTime && typeof activityCustomTime === 'string'
    ? activityCustomTime.split(',')
    : ''

let params = ref<Activity.SearchParams>({
  area: [],
  companyCategory: [],
  activityStatus: [],
  activityTime: [],
  activityType: [],
})

const search = async (url: string, query: object) => {
  const { searchParams, list, count, limit } = (await request({
    url,
    query,
  })) as Activity.Data

  activityList.value = list
  activityListCount.value = count
  activityPageSize.value = limit
  params.value = searchParams
}

const handleChange = async (query: Filter) => {
  const { length } = Object.keys(query)

  const api = `/api/activity/${length ? apiSuffix : 'list'}`
  currentPage.value = 1
  await search(api, { ...query, page: 1 })
  if (length) handleScroll(true)
}

const currentPageChange = async (page: number) => {
  const { query } = route
  const fetchQuery = { ...query, page }
  currentPage.value = page
  await search(`/api/activity/${apiSuffix}`, fetchQuery)
  handleScroll(true)
}

try {
  await search(`/api/activity/${apiSuffix}`, query)
} catch {
  navigateTo('/error')
}

const {
  banner,
  topShowcaseList,
  bottomShowcaseList,
  moreActivityList,
  newsList,
} = (await request({
  url: '/api/activity/index',
})) as Activity.Data

const menuList = computed(() => [
  { name: '归国活动', visible: true },
  { name: '更多活动展示', visible: hasData(moreActivityList) },
  {
    name: '活动动态速递',
    visible: hasData(newsList.topNews) || hasData(newsList.pageList),
  },
])

const handleScroll = (scroll = false) => {
  const {
    params: { path },
    query,
  } = route

  const dynamicRoute = hasData(path) || hasData(query) || scroll
  setTimeout(() => {
    if (dynamicRoute) scrollToTarget('归国活动')
  }, 300)
}

onMounted(() => {
  handleScroll()
})
</script>

<style lang="scss" scoped>
.activity__container {
  .company__wrapper {
    padding-top: 5px;
    padding-bottom: 1px;
  }

  .come__activity {
    padding-bottom: 60px;
    background:
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/activity/native-bg.png)
        no-repeat right bottom/322px 269px,
      var(--background-primary);

    .activity--icon {
      width: 60px;
      height: 40px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/activity/aircraft.png)
        no-repeat left/60px 40px;
      margin-right: 5px;
    }

    .abroad-activity__content {
      margin: 20px 0;

      .global-activity__container {
        padding-bottom: 20px;
      }
    }
  }

  .more__activity {
    padding-bottom: 48px;

    .title__container {
      padding-bottom: 50px;
    }
  }

  .news__activity {
    padding-bottom: 40px;
  }

  .empty-container {
    padding: 70px 0;
    margin-top: 20px;
  }
}

:deep() {
  .more-activity__address {
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png)
      no-repeat left/12px 12px;
  }
}
</style>
