<template>
  <div class="talent__container">
    <app-menu :data="menuList" />

    <base-banner :data="banner" class="banner__wrapper" />

    <div class="company__wrapper background-color--primary">
      <div class="view--width">
        <base-advertise-list :data="{ bottomShowcaseList, topShowcaseList }" />
      </div>
    </div>

    <div id="出海活动博览" class="aboard__activity">
      <div class="view--width">
        <base-title
          prefix="出海活动"
          suffix="博览"
          english="Exhibition of Overseas Activities"
        >
          <template #prefix-icon>
            <div class="activity--icon"></div>
          </template>
        </base-title>

        <talent-filter
          :data="params"
          :value="{ date }"
          @change="handleChange"
        />

        <template v-if="abroadList.length">
          <div class="abroad-activity__content">
            <base-global-activity
              type="abroad"
              :data="item"
              v-for="item in abroadList"
            />
          </div>

          <div class="flex--row flex-justify--center">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="abroadListCount"
              :current-page="currentPage"
              :default-page-size="abroadPageSize"
              @current-change="currentPageChange"
            />
          </div>
        </template>
        <template v-else>
          <div
            class="empty__wrapper flex--column flex--center background-color--default border-radius"
          >
            <base-empty />
          </div>
        </template>
      </div>
    </div>

    <div
      id="更多活动展示"
      v-if="hasData(moreActivityList)"
      class="more__activity"
    >
      <div class="view--width">
        <base-title
          prefix="更多活动"
          suffix="展示"
          english="More Event Displays"
        />

        <talent-activity :data="moreActivityList" type="abroad" />
      </div>
    </div>

    <div
      id="活动动态速递"
      v-if="hasData(newsList.topNews) || hasData(newsList.pageList)"
      class="news__activity background-color--primary"
    >
      <div class="view--width">
        <base-title
          prefix="活动动态"
          suffix="速递"
          english="Activity Dynamic Express"
        />

        <base-news-activity :data="newsList" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Filter {
  date: String[]
}

const abroadList = ref([])
const abroadListCount = ref(0)
const abroadPageSize = ref(0)
const currentPage = ref(1)

const route = useRoute()
const {
  query,
  query: { activityCustomTime, page = 1 },
  params: { path },
} = route

currentPage.value = Number(page)
const apiSuffix = Array.isArray(path) ? path.join('/') : 'list'

const date =
  activityCustomTime && typeof activityCustomTime === 'string'
    ? activityCustomTime.split(',')
    : ''

let params = ref<Talent.SearchParams>({
  area: [],
  activityStatus: [],
  activityTime: [],
  activityType: [],
})

const search = async (url: string, query: object) => {
  const { searchParams, list, count, limit } = (await request({
    url,
    query,
  })) as Talent.Data

  abroadList.value = list
  abroadListCount.value = count
  abroadPageSize.value = limit
  params.value = searchParams
}

const handleChange = async (query: Filter) => {
  const { length } = Object.keys(query)

  const api = `/api/talent/${length ? apiSuffix : 'list'}`
  currentPage.value = 1
  await search(api, { ...query, page: 1 })
  if (length) handleScroll(true)
}

const currentPageChange = async (page: number) => {
  const { query } = route
  const fetchQuery = { ...query, page }
  currentPage.value = page
  await search(`/api/talent/${apiSuffix}`, fetchQuery)
  handleScroll(true)
}

try {
  await search(`/api/talent/${apiSuffix}`, query)
} catch {
  navigateTo('/error')
}

const {
  banner,
  bottomShowcaseList,
  moreActivityList,
  newsList,
  topShowcaseList,
} = (await request({
  url: '/api/talent/index',
})) as Talent.Data

const menuList = computed(() => [
  { name: '出海活动博览', visible: true },
  { name: '更多活动展示', visible: hasData(moreActivityList) },
  {
    name: '活动动态速递',
    visible: hasData(newsList.topNews) || hasData(newsList.pageList),
  },
])

const handleScroll = (scroll = false) => {
  const {
    params: { path },
    query,
  } = route

  const dynamicRoute = hasData(path) || hasData(query) || scroll
  setTimeout(() => {
    if (dynamicRoute) scrollToTarget('出海活动博览')
  }, 300)
}

onMounted(() => {
  handleScroll()
})
</script>

<style lang="scss" scoped>
.talent__container {
  .company__wrapper {
    padding-top: 5px;
    padding-bottom: 1px;
  }

  .aboard__activity {
    padding-bottom: 60px;
    background:
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/talent/abroad-bg.png)
        no-repeat right bottom/610px 425px,
      var(--background-primary);

    .activity--icon {
      width: 37px;
      height: 43px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/talent/boat.png)
        no-repeat left/37px 43px;
      margin-right: 5px;
    }

    .abroad-activity__content {
      margin: 20px 0;

      .global-activity__container {
        padding-bottom: 20px;
      }
    }
  }

  .more__activity {
    padding-bottom: 48px;

    .title__container {
      padding-bottom: 50px;
    }
  }

  .news__activity {
    padding-bottom: 40px;
  }

  .empty__wrapper {
    padding: 70px 0;
    margin-top: 20px;
  }
}
</style>
