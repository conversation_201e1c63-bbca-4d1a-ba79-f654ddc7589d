<template>
  <div class="youth__container">
    <app-menu :data="menuList" />

    <base-banner :data="banner" />

    <div class="background-color--primary youth__product">
      <div class="view--width">
        <div id="重磅公告">
          <announcement-heavy :data="headlinesList" />
        </div>

        <div class="youth__introduce" id="项目介绍">
          <base-title
            prefix="海外优青"
            suffix="项目介绍"
            reverse
            english="Introduction"
            tail
          />

          <div class="youth__text font-size--18 border-radius">
            <div>
              <span class="font-weight--bold"
                >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;海外优青</span
              >
              <span>{{ projectInfo.description }}</span>
            </div>

            <div class="youth__subtitle flex--row flex-justify--center">
              <div class="youth__item font-weight--bold youth__products">
                国家级青年人才项目
              </div>
              <div class="youth__item font-weight--bold youth__platform">
                优质发展平台与广阔发展空间
              </div>
              <div class="youth__item font-weight--bold youth__guarantee">
                优厚待遇与全方位保障
              </div>
            </div>
          </div>

          <div class="flex--row flex-justify--between">
            <youth-news-card :data="projectInfo.trendsList" />
            <youth-news-card
              :title="'申报干货'"
              :class-name="'file'"
              :data="projectInfo.dryList"
            />
          </div>
        </div>
      </div>
    </div>

    <div
      class="background-color--default"
      id="推荐公告"
      v-if="
        hasData(recommendAnnouncementList.topList) ||
        hasData(recommendAnnouncementList.bottomList)
      "
    >
      <div class="view--width">
        <base-title
          prefix="推荐"
          suffix="公告"
          reverse
          english="Recommended Announcement"
          description="海外优青热门公告，诚邀英才依托申报！"
        />

        <div class="youth__notice-list">
          <ul class="youth__top flex--row">
            <li
              v-for="item in recommendAnnouncementList.topList"
              class="youth__top-item position--relative border-radius overflow--hidden animation-mouseover"
            >
              <img
                class="position--absolute image--cover"
                :src="item.image"
                :alt="item.title"
              />

              <div
                class="youth__top-content position--absolute position--center cursor--pointer"
              >
                <a
                  :href="item.url"
                  class="flex--column"
                  target="_blank"
                  :title="item.title"
                  @click="addShowcase(item.id, item.number)"
                >
                  <div
                    class="youth__top-company flex--row flex-align--center"
                    v-if="item.companyName"
                  >
                    <img
                      :src="item.companyLogo"
                      :alt="item.companyName"
                      class="image--contain image--logo"
                    />
                    <div
                      class="overflow--ellipsis color--white youth__top--name"
                    >
                      {{ item.companyName }}
                    </div>
                  </div>

                  <div class="youth__top-title font-weight--bold color--white">
                    {{ item.title }}
                  </div>
                </a>

                <a
                  :href="item.url"
                  :title="item.title"
                  class="youth__button font-weight--bold background-color--default border-radius--16 flex--row flex-align--center flex-justify--center"
                  target="_blank"
                  @click="addShowcase(item.id, item.number)"
                >
                  立即申报
                </a>
              </div>
            </li>
          </ul>

          <div class="flex--row">
            <youth-notice-card
              v-for="item in recommendAnnouncementList.bottomList"
              class="youth__recommend-notice"
              :data="item"
            />
          </div>
        </div>
      </div>
    </div>

    <div
      class="background-color--primary youth__company-container"
      id="依托单位推荐"
      v-if="hasData(relyCompanyList)"
    >
      <div class="view--width">
        <base-title
          prefix="依托单位"
          suffix="推荐"
          reverse
          english="Unit Recommendation"
          description="高水平单位重磅引才，前景广阔，待遇从优！"
        />

        <div class="youth__company-list flex--row flex--wrap">
          <a
            v-for="item in relyCompanyList"
            class="youth__company display--block border-radius background-color--default animation-mouseover--border box-shadow cursor--pointer"
            :href="item.url"
            :title="item.title"
            target="_blank"
            rel="nofollow"
            @click="addShowcase(item.id, item.number)"
          >
            <img class="image--contain" :src="item.image" :alt="item.title" />

            <div class="font-size--16 font-weight--bold youth__title">
              {{ item.title }}
            </div>

            <div
              class="youth__company-bottom overflow--ellipsis box-sizing--border"
            >
              {{ item.subTitle }}
            </div>
          </a>
        </div>
      </div>
    </div>

    <div
      class="background-color--default"
      id="近期活动"
      v-if="hasData(lastActivityList)"
    >
      <div class="view--width">
        <base-title
          prefix="近期"
          suffix="活动"
          reverse
          english="Recent  Activities"
          description="多元活动形式，直击海外优青项目的最新动态"
        />

        <div class="youth__active-list flex--row flex--wrap">
          <a
            class="youth__active-item display--block border-radius background-color--default animation-mouseover--border box-shadow cursor--pointer"
            v-for="item in lastActivityList"
            :href="item.url"
            target="_blank"
            :title="item.title"
            rel="nofollow"
            @click="addShowcase(item.id, item.number)"
          >
            <img
              class="border-radius image--cover"
              :src="item.image"
              :alt="item.title"
            />
            <div class="youth__active-title font-size--16 font-weight--bold">
              {{ item.title }}
            </div>
          </a>
        </div>
      </div>
    </div>

    <div
      class="background-color--primary youth__announcement-container"
      id="更多依托公告"
    >
      <div class="view--width">
        <base-title
          prefix="更多"
          suffix="依托公告"
          reverse
          english="More Announcements"
          description="海外优青公告汇总，全面呈现各地就业发展机遇"
        />

        <div class="youth__announcement-check" v-loading="loading">
          <el-radio-group
            @change="handleCheckChange"
            v-model="announcementParams.areaType"
          >
            <el-radio-button
              class="check-label"
              v-for="item in relyAnnouncementAreaList"
              :value="item.k"
              >{{ item.v }}</el-radio-button
            >
          </el-radio-group>

          <div v-if="hasData(announcement.list)">
            <base-youth-list
              :data="announcement.list"
              class="youth__more-announce"
            />
            <el-pagination
              class="youth__pagination flex--row flex-justify--center"
              background
              layout="prev, pager, next"
              :total="announcement.count"
              v-model:current-page="announcementParams.page"
              @current-change="(page: number) => handlePageChange(page)"
              :default-page-size="12"
            />
          </div>

          <div
            class="youth__announcement-empty flex--column flex--center background-color--default border-radius"
            v-else
          >
            <base-empty emptyType="公告" />
          </div>
        </div>
      </div>
    </div>

    <div class="background-color--default" id="人才自荐">
      <base-title
        prefix="人才"
        suffix="自荐"
        reverse
        english="Talent Self Recommendation"
        description="成功搭线100+人才与单位，更多曝光，更多机会！"
      />

      <div class="youth__recommend-list view--width position--relative">
        <el-carousel
          ref="carouselRef"
          trigger="click"
          height="420px"
          arrow="never"
          :autoplay="false"
          indicator-position="none"
        >
          <el-carousel-item
            v-for="(group, index) in caseDataGroup"
            :key="index"
          >
            <div class="youth__carousel flex--row flex-align--center">
              <div
                class="youth__carousel-item"
                v-for="(item, index) in group"
                :key="index"
              >
                <youth-card-talent :data="item" />
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>

        <span
          class="carousel--previous carousel--indicator position--absolute cursor--pointer"
          @click="() => handleCarousel(-1, 'talent')"
        />

        <span
          class="carousel--next carousel--indicator position--absolute cursor--pointer"
          @click="() => handleCarousel(1, 'talent')"
        />
      </div>

      <div class="youth__recommend-button flex--row flex-justify--center">
        <base-link
          width="138px"
          size="large"
          :text="'我要参与自荐'"
          rel="nofollow"
          :href="'https://f.wps.cn/g/DMBtJ1xd/'"
          class="linear-gradient--primary color--white youth__recommend-talent"
        />

        <base-link
          width="138px"
          size="large"
          :text="'我对人才感兴趣'"
          rel="nofollow"
          :href="'https://f.wps.cn/g/T1ZMD9SI/'"
        />
      </div>
    </div>

    <div class="background-color--primary">
      <div class="view--width youth__popular--container">
        <div id="热门关注">
          <base-title
            prefix="人气"
            suffix="关注排行"
            reverse
            english="Popular Following Ranking"
            description="热门公告，单位，资讯快人一步获悉！"
          />

          <div class="flex--row">
            <div class="youth__popular border-radius background-color--default">
              <div class="youth__popular-header flex--row">
                <div
                  class="youth__popular-check border-radius text-align--center font-size--20 cursor--pointer"
                  @click="handleCheckPopular('notice')"
                  :class="popularHeadClass ? 'active' : ''"
                >
                  热门关注公告
                </div>

                <div
                  class="youth__popular-check youth__check-company text-align--center font-size--20 cursor--pointer"
                  @click="handleCheckPopular('company')"
                  :class="popularHeadClass ? '' : 'active'"
                >
                  热门关注单位
                </div>
              </div>

              <div
                class="youth__popular-list flex--wrap overflow--hidden"
                :class="popularHeadClass ? 'list-active' : ''"
              >
                <div
                  v-for="(item, index) in hotAnnouncement"
                  :key="index"
                  class="youth__popular-item background-color--primary flex--row flex-align--center flex-justify--between"
                  :class="`youth__title-${index}`"
                >
                  <base-sort :index="index" size="small" />

                  <a
                    :href="item.url"
                    class="youth__link cursor--pointer overflow--ellipsis"
                    target="_blank"
                    :title="item.title"
                    @click="addShowcase(item.id, item.number)"
                    >{{ item.title }}</a
                  >

                  <div class="font-color--basic">{{ item.refreshDate }}</div>
                </div>
              </div>

              <div
                class="youth__popular-list flex--wrap overflow--hidden"
                :class="popularHeadClass ? '' : 'list-active'"
              >
                <a
                  v-for="(item, index) in hotCompany"
                  :key="item.id"
                  class="youth__company-item border-radius flex--row flex-align--center"
                >
                  <div
                    class="youth__sort color--white font-size--16 text-align--center"
                  >
                    0{{ index + 1 }}
                  </div>

                  <div class="youth__info overflow--ellipsis">
                    <a
                      :href="item.url"
                      :title="item.companyName"
                      target="_blank"
                      class="font-size--16 font-weight--bold cursor--pointer"
                      >{{ item.companyName }}</a
                    >
                    <div class="font-color--basic overflow--ellipsis">
                      {{ item.info }}
                    </div>
                  </div>
                </a>
              </div>
            </div>

            <div class="youth__news border-radius background-color--default">
              <div class="youth__news-header">
                <div class="font-size--20 font-weight--bold youth__mb-10">
                  相关资讯
                </div>

                <div class="youth__news-tips">申报经验/人物故事/评论热议</div>
              </div>

              <el-carousel arrow="never" :autoplay="false" height="400px">
                <el-carousel-item
                  v-for="(item, index) in relateNewsList"
                  :key="index"
                >
                  <a
                    :href="k.url"
                    class="youth__point overflow overflow--ellipsis cursor--pointer"
                    :title="k.title"
                    v-for="k in item"
                    target="_blank"
                    rel="nofollow"
                  >
                    {{ k.title }}
                  </a>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div>

        <div
          class="flex--row flex-align--center"
          id="更多活动"
          v-if="hasData(moreActivityList)"
        >
          <base-title
            title="更多活动"
            primary
            english="More Activities"
            description="或许您还对这些内容感兴趣"
          />

          <div class="youth__more-list position--relative">
            <el-carousel
              ref="carouselMoreRef"
              trigger="click"
              height="215px"
              arrow="never"
              :autoplay="false"
              indicator-position="none"
            >
              <el-carousel-item
                v-for="(group, index) in moreActiveGroup"
                :key="index"
              >
                <div
                  class="youth__carousel flex--row flex-align--center more-active"
                >
                  <a
                    v-for="item in group"
                    :href="item.url"
                    :title="item.title"
                    rel="nofollow"
                    class="youth__more-card border-radius box-shadow background-color--default cursor--pointer"
                    target="_blank"
                    @click="addShowcase(item.id, item.number)"
                  >
                    <img class="" :src="item.image" :alt="item.title" />

                    <div class="youth__more-title background-color--default">
                      <div class="font-weight--bold youth__active-text">
                        {{ item.title }}
                      </div>
                    </div>
                  </a>
                </div>
              </el-carousel-item>
            </el-carousel>

            <span
              class="carousel--previous carousel--indicator position--absolute cursor--pointer more--previous"
              @click="() => handleCarousel(-1)"
            />

            <span
              class="carousel--next carousel--indicator position--absolute cursor--pointer more--next"
              @click="() => handleCarousel(1)"
            />
          </div>
        </div>
      </div>
    </div>

    <div
      class="youth__banner flex--column flex-align--center flex-justify--center"
    >
      <div class="font-size--32 color--white font-weight--bold">
        欢迎用人单位选择高才海外优青专项服务
      </div>

      <div class="color--white youth__banner__text">
        我们将提供宣传推广、线上直播、线下对接会、人选对接等多元服务，助力用人单位在海外优青申报阶段加速引才，高效储备更多海外青年优才
      </div>
    </div>

    <div class="background-color--default">
      <div class="view--width">
        <div id="海优服务介绍">
          <base-title
            prefix="海外优青"
            suffix="专项服务"
            reverse
            english="Special Services"
            description="全球曝光，高效直面，精准对接，快速揽才"
          />

          <div class="youth__special">
            <div class="youth__special-container">
              <el-tabs
                type="card"
                class="youth__special-check"
                v-model="serviceActive"
              >
                <el-tab-pane label="网站&新媒体宣传" name="first">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special1.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold">
                        网站&新媒体宣传
                      </div>
                      <div
                        class="font-size--18 font-weight--bold youth__special-title youth__special-pc"
                      >
                        高校人才网官网
                      </div>

                      <div
                        class="youth__count background-color--default flex--row flex-align--center flex-justify--between"
                      >
                        <div>
                          日页面浏览量近<span
                            class="color--primary font-weight--bold"
                            >80万</span
                          >
                        </div>

                        <div>
                          日访客量近<span
                            class="color--primary font-weight--bold"
                            >10万</span
                          >
                        </div>

                        <div>
                          年均海外访客超<span
                            class="color--primary font-weight--bold"
                            >65万</span
                          >人次
                        </div>
                      </div>

                      <div class="flex--row">
                        <div class="point-primary mr-30 font-size--16">
                          搭建海外优青引才专题聚合推广
                        </div>

                        <div class="point-primary font-size--16">
                          官网首页及高才海外栏目持续曝光
                        </div>
                      </div>

                      <div
                        class="font-size--18 font-weight--bold youth__special-title youth__special-wechat"
                      >
                        官方微信公众号
                      </div>

                      <div
                        class="youth__count background-color--default flex--row flex-align--center flex-justify--between"
                      >
                        <div>
                          覆盖海内外<span
                            class="color--primary font-weight--bold"
                            >100万+</span
                          >硕博人才
                        </div>
                      </div>

                      <div class="point-primary font-size--16">
                        官微“高才-高校人才网”+官微“高才海外HiTalentGlobal”联合推广
                      </div>

                      <div
                        class="font-size--18 font-weight--bold youth__special-title youth__special-community"
                      >
                        海外博士社群
                      </div>

                      <div
                        class="youth__count background-color--default flex--row flex-align--center flex-justify--between"
                      >
                        <div>
                          <span class="color--primary font-weight--bold"
                            >41个</span
                          >海归博士社群
                        </div>

                        <div>
                          <span class="color--primary font-weight--bold"
                            >7400+</span
                          >海外博士社群用户
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="海优直播推介会" name="second">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special2.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold youth__mb-15">
                        海优直播推介会
                      </div>

                      <div class="font-color--label youth__special-description">
                        为用人单位搭建专属直播间开展海外优青专题直播活动，通过线上宣讲及答疑互动，帮助用人单位实现全球高层次人才对接联动，精准引才。
                      </div>

                      <div class="point-primary font-size--16 youth__mb-18">
                        开播时定向邀约人才参会，提高参会率
                      </div>

                      <div class="point-primary font-size--16 youth__mb-18">
                        创建专属社群，实时为人才答疑，聚集并引导目标人才投递
                      </div>

                      <div class="point-primary font-size--16">
                        支持视频直播、PPT展示、聊天互动等，增强互动及参会体验感
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="线下人才对接会" name="third">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special7.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold youth__mb-15">
                        线下人才对接会
                      </div>

                      <div class="font-color--label youth__special-description">
                        高校人才网自2019年办会以来，已成功举办了超百场海内外引才活动，涵盖自主创办的"全国巡回招聘会"、"海（境）外组团招聘会"等大型线下招聘会，以及与海交会组委会联合举办的"2023海交会之青年科技人才招聘会暨海外优青人才项目交流对接会"和"2024海交会之青年科技人才嘉年华（招聘会）暨项目人才交流对接会"等，活动吸引了上万名海外人才参与。用人单位可通过参加高校人才网的线下组团招聘会与目标人才进行线下对接，做好海优人才储备。
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18"
                      >
                        线下活动形式多样，用人单位可灵活匹配
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18"
                      >
                        一次性直面不同领域的优秀人才，效率更高
                      </div>

                      <div class="youth__common-point font-size--16">
                        在活动中扩大雇主品牌推广，提高影响力
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="海外引才渠道宣传" name="fourth">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special4.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold youth__mb-15">
                        海外引才渠道宣传
                      </div>

                      <div class="font-color--label youth__special-description">
                        为用人单位提供超百家QS排名前列的境外高校学联、博士协会等学者渠道选择，覆盖北美、欧洲、新加坡、日本、澳大利亚、中国香港等重点区域。
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18"
                      >
                        通过海（境）外顶尖高校学联渠道增强曝光
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18"
                      >
                        快速触达海（境）外目标院校人才，精准招引
                      </div>

                      <div class="youth__common-point font-size--16">
                        以图文形式吸引更多海外菁英关注了解
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="海优人选猎头服务" name="five">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special5.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold youth__mb-15">
                        海优人选猎头服务
                      </div>

                      <div class="font-color--label youth__special-description">
                        依托平台多元化的宣传渠道、40万+高层次人才库、资深的猎头经验以及成熟的人选寻访能力，将为用人单位提供强有力的人才资源支持和高效精细的人才寻猎服务，帮助单位快速获取目标人才。
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18 flex--row"
                      >
                        资深专业的能力：20+资深人才运营团队，5年以上猎头从业经验，提供一对一专属顾问对接
                      </div>

                      <div
                        class="youth__common-point font-size--16 youth__mb-18 flex--row"
                      >
                        精细化服务：提供宣传推广、人才寻访、简历收集&筛选、人才推荐等服务，
                        助力用人单位完成高层次人才寻猎，加速引才，省时省力
                      </div>

                      <div class="youth__common-point font-size--16 flex--row">
                        以结果为导向：为单位推荐满足海优申报基础条件的人选，为单位储备海外人才省时提效
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="海优人选自荐" name="six">
                  <div class="youth__special-item flex--row flex-align--center">
                    <img
                      class="border-radius"
                      src="https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special6.png"
                      alt=""
                    />

                    <div class="youth__special-text">
                      <div class="font-size--24 font-weight--bold youth__mb-15">
                        海优人选自荐
                      </div>

                      <div class="font-color--label youth__special-description">
                        为帮助供需双方提高对接效率和成功率，将给有兴趣申报海优项目的人才提供简历自荐展示的平台，促进人才第一时间收获更多优质单位的关注；同时用人单位可在人才大厅筛选适岗人才，并在完成意向申请后，由专业人才顾问协助单位与目标人才进行意向对接，还能获取更多人才推荐。
                      </div>

                      <div
                        class="font-size--16 youth__mb-18 youth__common-point"
                      >
                        多维度展示人才信息，人才优势一目了然，有助于供需双方加速匹配
                      </div>

                      <div
                        class="font-size--16 youth__mb-18 youth__common-point"
                      >
                        单位可通过人才自荐或专业人才顾问推荐筛选人才，提前锁定更多优质候选人
                      </div>

                      <div class="font-size--16 youth__common-point">
                        人才可通过自荐迅速脱颖而出，还能通过客服推荐提高求职成功率
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>

        <base-title
          prefix="服务"
          suffix="优势"
          reverse
          english="Service Advantages"
          description="多渠道精准宣传，多形式人才邀约，强有力服务保障"
        />

        <div class="youth__service flex--row">
          <div
            class="youth__service-item border-radius position--relative"
            v-for="item in serviceList"
            :class="`youth__service-item-${item.id}`"
          >
            <div class="youth__service-container">
              <div class="youth__service-text position--absolute">
                <div class="color--white font-size--18 font-weight--bold">
                  {{ item.title }}
                </div>
                <div class="color--white font-size--18 font-weight--bold">
                  {{ item.subtitle }}
                </div>
              </div>

              <div class="youth__service-mask position--absolute border-radius">
                <div class="color--white font-size--18 font-weight--bold">
                  {{ item.title }}
                </div>
                <div class="color--white font-size--18 font-weight--bold">
                  {{ item.subtitle }}
                </div>

                <div class="youth__service-description color--white">
                  {{ item.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="youth__case" v-if="hasData(cooperationCaseList)">
          <base-title
            prefix="合作"
            suffix="案例"
            reverse
            english="Collaboration Cases"
          />

          <div class="flex--row">
            <base-collaboration-case
              class="youth__case-item"
              v-for="item in cooperationCaseList"
              :data="item"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Reactive } from 'vue'

const popularHeadClass = ref(true)

const loading = ref(false)

const serviceActive = ref('first')

const carouselRef = ref()

const carouselMoreRef = ref()

const announcement: Reactive<
  Pick<Youth.RelyAnnouncementList, 'list' | 'count'>
> = reactive({
  list: [],
  count: 1,
})

let announcementParams: Reactive<Youth.Request> = reactive({
  areaType: 0,
  page: 1,
})

const {
  banner,
  headlinesList,
  recommendAnnouncementList,
  projectInfo,
  relyCompanyList,
  lastActivityList,
  moreActivityList,
  relyAnnouncementAreaList,
  relyAnnouncementList,
  relateNewsList,
  cooperationCaseList,
  serviceList = [
    {
      title: '自有渠道+海外渠道联动造势，',
      subtitle: '全方位触达全球优质人才',
      description:
        '高才科技深耕行业18载，已打造出全面、精准且备受高层次人才关注的自有媒体矩阵。同时，我们与海（境）外超过百家学联、博士博士后学者联盟渠道建立了紧密的合作关系。针对海外优青专项服务，我们将充分发挥高才科技在渠道宣传方面的优势，通过官网、微信公众号、社群以及海外渠道等多维度发力，助力用人单位全面触达海外优质人才。',
      id: '1',
    },
    {
      title: '丰富的海外人才资源储备，',
      subtitle: '广泛覆盖与邀约目标人才',
      description:
        '截至目前，高才科技已积累了万余名海外博士层次以上的学者资源。在承接举办的多场海外引才活动中，高才科技通过前期精准宣传、搭建专属社群、实施人才一对一邀约等策略，成功促进了活动报名人才的到会率高达90%。针对海外优青专项服务，高才科技将充分发挥在海外人才资源方面的优势，充分发挥目标人选资源，通过网络、邮件等多种方式广泛覆盖并有效触达每一位目标人才。',
      id: '2',
    },
    {
      title: '专业团队+丰富经验，',
      subtitle: '保障项目落地成效',
      description:
        '2022年以来，高才科技围绕海外优青服务，专门搭建网站和新媒体平台，组建了一支超过20人的专业服务团队，形成了境内+境外、线上+线下相结合的多种产品服务模式，覆盖了宣传曝光、活动执行、人选交付等各个环节。迄今为止，我们已经为数十家国内知名高校、科研院所、企事业单位提供了专项服务，积累了丰富的项目经验，为客户交付提供了坚实的服务保障。',
      id: '3',
    },
  ],
  hotAnnouncement,
  hotCompany,
  talentRecommend = [
    {
      name: '陈博士',
      age: '30-35',
      experience: '13-24个月',
      id: '146222',
      avatar: '',
      school: '伊利诺伊大学芝加哥分校',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '机械制造与电气动力类' },
        { title: '论文被引次数', major: '[51-100]区间' },
        { title: '论文累计影响因子', major: '[20-40]区间' },
        { title: '意向依托单位类型', major: '高校、科研院所、医院等' },
        { title: '意向区域', major: '华东、华南、华中地区' },
      ],
      avatarGender: 'female',
    },
    {
      name: '赵博士',
      age: '30-35',
      experience: '25个月及以上',
      id: '415826',
      avatar: '',
      school: '中国科学院上海生命科学研究院',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '生命科学类' },
        { title: '论文被引次数', major: '[101-200]区间' },
        { title: '论文累计影响因子', major: '[100-150]区间' },
        { title: '意向依托单位类型', major: '双一流高校、医院等' },
        { title: '意向区域', major: '华东地区' },
      ],
      avatarGender: 'male',
    },
    {
      name: '李博士',
      age: '30-35',
      experience: '24个月以上',
      id: '006012',
      avatar: '',
      school: '新加坡南洋理工大学',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '工程力学' },
        { title: '论文被引次数', major: '550次' },
        { title: '论文累计影响因子', major: '100' },
        { title: '意向依托单位类型', major: '双一流高校' },
        { title: '意向区域', major: '北京/上海' },
      ],
      avatarGender: 'male',
    },
    {
      name: '杨博士',
      age: '30-35',
      experience: '24个月以上',
      id: '000020',
      avatar: '',
      school: '广东工业大学',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '信息与通信工程' },
        { title: '论文被引次数', major: '150余次' },
        { title: '论文累计影响因子', major: '85.6' },
        { title: '意向依托单位类型', major: '双一流高校' },
        { title: '意向区域', major: '广州' },
      ],
      avatarGender: 'female',
    },
    {
      name: '李博士',
      age: '35-40',
      experience: '36个月以上',
      id: '007031',
      avatar: '',
      school: '复旦大学',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '材料科学与工程，电化学，能源存储' },
        { title: '论文被引次数', major: '224次' },
        { title: '论文累计影响因子', major: '53.2' },
        { title: '意向依托单位类型', major: '双一流高校' },
        { title: '意向区域', major: '上海' },
      ],
      avatarGender: 'male',
    },
    {
      name: '俞博士',
      age: '35-40',
      experience: '48个月以上',
      id: '006004',
      avatar: '',
      school: '加拿大约克大学',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '大气科学，人工智能在气象中的应用' },
        { title: '论文被引次数', major: '135次' },
        { title: '论文累计影响因子', major: '63' },
        { title: '意向依托单位类型', major: '双一流高校/科研机构' },
        { title: '意向区域', major: '广州/南宁' },
      ],
      avatarGender: 'female',
    },
    {
      name: '梁博士',
      age: '35-40',
      experience: '36个月以上',
      id: '003005',
      avatar: '',
      school: '德国卡尔斯鲁厄理工学院',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '生物传感器技术，微生物降解与解毒' },
        { title: '论文被引次数', major: '26次' },
        { title: '论文累计影响因子', major: '142.4' },
        { title: '意向依托单位类型', major: '双一流高校/科研机构' },
        { title: '意向区域', major: '安徽/江苏' },
      ],
      avatarGender: 'female',
    },
    {
      name: '马博士',
      age: '30-35',
      experience: '36个月以上',
      id: '004016',
      avatar: '',
      school: '香港中文大学',
      schoolType: '博士培养院校',
      experienceList: [
        { title: '学科领域方向', major: '常见慢性病危险因素、人群身心健康等' },
        { title: '论文被引次数', major: '3次' },
        { title: '论文累计影响因子', major: '54.9' },
        { title: '意向依托单位类型', major: '双一流高校/科研机构' },
        { title: '意向区域', major: '上海/杭州' },
      ],
      avatarGender: 'male',
    },
    {
      name: '王博士',
      age: '30-35',
      experience: '36个月以上',
      id: '002011',
      avatar: '',
      school: '英国贝尔法斯特女王大学',
      schoolType: '博士培养院校',
      experienceList: [
        {
          title: '学科领域方向',
          major: '氧化石墨烯的导电性与功函数控制研究等',
        },
        { title: '论文被引次数', major: '27次' },
        { title: '论文累计影响因子', major: '30.2' },
        { title: '意向依托单位类型', major: '科研机构' },
        { title: '意向区域', major: '成都/深圳' },
      ],
      avatarGender: 'female',
    },
  ],
} = (await request({
  url: '/api/youth/index',
})) as Youth.Data

const menuList = computed(() => [
  { name: '重磅公告', visible: true },
  { name: '项目介绍', visible: true },
  {
    name: '推荐公告',
    visible:
      hasData(recommendAnnouncementList.topList) ||
      hasData(recommendAnnouncementList.bottomList),
  },
  { name: '依托单位推荐', visible: hasData(relyCompanyList) },
  { name: '近期活动', visible: hasData(lastActivityList) },
  { name: '更多依托公告', visible: true },
  { name: '人才自荐', visible: true },
  { name: '热门关注', visible: true },
  { name: '更多活动', visible: hasData(moreActivityList) },
  { name: '海优服务介绍', visible: true },
])

const caseDataGroup = computed(() => {
  const talent = talentRecommend.reduce(
    (
      result: Youth.Data['talentRecommend'],
      current: Youth.Data['talentRecommend'],
      index: number,
    ) => {
      if (index % 3 === 0) {
        result.push([current])
      } else {
        result.at(-1).push(current)
      }
      return result
    },
    [],
  )

  return talent
})

const moreActiveGroup = computed(() => {
  const talent = moreActivityList.reduce(
    (
      result: Youth.Data['moreActivityList'],
      current: Youth.Data['moreActivityList'],
      index: number,
    ) => {
      if (index % 3 === 0) {
        result.push([current])
      } else {
        result.at(-1).push(current)
      }
      return result
    },
    [],
  )

  return talent
})

announcement.list = relyAnnouncementList.list
announcement.count = relyAnnouncementList.count

const search = async () => {
  loading.value = true
  const { list, count } = (await request({
    url: '/api/youth/get-rely-announcement-list',
    query: announcementParams,
  })) as Youth.Data

  announcement.list = list
  announcement.count = count
  loading.value = false
}

const handlePageChange = (val: number) => {
  announcementParams.page = val

  search()

  scrollToTarget('更多依托公告')
}

const handleCheckPopular = (type: string) => {
  if (type === 'notice') {
    popularHeadClass.value = true
    return
  }

  popularHeadClass.value = false
}

const handleCheckChange = (value: string | number | boolean | undefined) => {
  announcementParams.areaType = value as number

  search()
}

const handleCarousel = (value: number, type?: string) => {
  const action = value > 0 ? 'next' : 'prev'

  if (type === 'talent') {
    carouselRef.value[action]()
    return
  }

  carouselMoreRef.value[action]()
}
</script>

<style scoped lang="scss">
.youth {
  &__container {
    margin-bottom: 60px;
  }

  &__product {
    padding: 60px 0;
  }

  &__text {
    line-height: 2;
    background: linear-gradient(90deg, #ecf4ff, #dfe6fd);
    position: relative;
    padding: 35px 30px;
    margin-bottom: 30px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 644px;
      height: 165px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/product-bg.png)
        no-repeat right center/cover;
    }
  }

  &__subtitle {
    margin-top: 30px;
  }

  &__item {
    padding: 0 50px 0 70px;
    border-right: 1px solid #90b2ff;
    height: 18px;
    line-height: 1;
    color: #285ba2;
    background-repeat: no-repeat;
    background-size: 28px;
    background-position: left 35px center;

    &:last-of-type {
      border: none;
    }
  }

  $iconList: products, platform, guarantee;

  @each $icon in $iconList {
    &__#{$icon} {
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/#{$icon}.png);
    }
  }

  &__banner {
    height: 185px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/banner.png)
      no-repeat center / cover;

    &__text {
      margin-top: 20px;
    }
  }

  &__notice {
    &-list {
      margin-bottom: 60px;
    }
  }

  &__recommend {
    &-notice {
      margin-right: 20px;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  &__top {
    &-item {
      width: 590px;
      height: 248px;
      margin-bottom: 20px;
      margin-right: 20px;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          #234283 0%,
          rgba(30, 63, 132, 0.38) 77%,
          rgba(164, 192, 250, 0.2) 100%
        );
      }

      &:last-of-type {
        margin-right: 0;
      }
    }

    &--name {
      max-width: 340px;
    }

    &-content {
      padding: 0 30px;
      width: 100%;
      z-index: 1;

      img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 12px;
      }
    }

    &-title {
      max-width: 397px;
      @include ellipsis-lines(2, 40px, 24px);
    }

    &-company {
      margin-bottom: 10px;
    }
  }

  &__active {
    &-list {
      padding-bottom: 60px;
    }

    &-item {
      width: 285px;
      height: 378px;
      padding: 20px 15px;
      margin-right: 20px;

      img {
        width: 253px;
        height: 280px;
      }

      &:last-of-type {
        margin-right: 0;
      }
    }

    &-title {
      margin-top: 10px;
      @include ellipsis-lines(2, 24px, 16px);
    }

    &-text {
      @include ellipsis-lines(2, 21px);
    }
  }

  &__announcement {
    &-check {
      .check-label {
        margin-right: 15px;
        margin-bottom: 20px;

        :deep(.el-radio-button__inner) {
          width: 227px;
          height: 40px;
          border-radius: 8px;
          line-height: 24px;
          border: 1px solid var(--color-border-default);
          box-shadow: none;
        }

        &:nth-of-type(5n) {
          margin-right: 0px;
        }
      }
    }

    &-container {
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/announce-bg.png);
      background-repeat: no-repeat;
      background-size: 366px 416px;
      background-position: right bottom;
      padding-bottom: 60px;
    }

    &-empty {
      width: 100%;
      padding: 50px 0;
      margin-top: 40px;
    }
  }

  &__pagination {
    margin-top: 40px;
  }

  &__button {
    width: 96px;
    height: 32px;
    margin-top: 20px;
    color: var(--color-primary);
  }

  &__company {
    width: 285px;
    height: 180px;
    padding: 10px 15px;
    margin-right: 20px;
    margin-bottom: 20px;

    &-container {
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/recommend-bg.png);
      background-repeat: no-repeat;
      background-size: 637px 276px;
      background-position: right bottom;
      padding-bottom: 40px;
    }

    &:nth-of-type(4n) {
      margin-right: 0px;
    }

    img {
      width: 253px;
      height: 50px;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    &-bottom {
      border-top: 1px solid var(--color-border);
      color: rgba(#9f6d1a, 0.8);
      padding-left: 30px;
      padding-top: 10px;
      height: 30px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/praise.png)
        no-repeat left bottom / 26px 22px;
    }
  }

  &__title {
    @include ellipsis-lines(2, 24px, 16px);
    margin-bottom: 12px;
  }

  &__service {
    &-item {
      width: 386px;
      height: 440px;
      margin-right: 20px;

      @for $i from 1 through 3 {
        &-#{$i} {
          &::before {
            content: '';
            display: block;
            width: 386px;
            height: 440px;
            background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/service-#{$i}.png)
              no-repeat
              center/cover;
          }
        }
      }

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        .youth__service-text {
          display: none;
        }

        .youth__service-mask {
          display: block;
        }
      }
    }

    &-text {
      left: 30px;
      bottom: 30px;
      line-height: 27px;
    }

    &-mask {
      display: none;
      width: 100%;
      height: 100%;
      top: 0;
      line-height: 27px;
      background: linear-gradient(
        180deg,
        #1b3367 0%,
        rgba(11, 29, 66, 0.38) 77%,
        rgba(164, 192, 250, 0.2) 100%
      );
      padding: 60px 30px;
    }

    &-description {
      margin-top: 20px;
    }
  }

  &__special {
    :deep(.el-tabs__header) {
      border-bottom: none;

      .el-tabs__nav {
        border: none;
        float: none;
      }

      .el-tabs__item {
        width: 186px;
        height: 40px;
        border: 1px solid var(--color-border);
        margin-right: 20px;
        border-radius: 20px;
        color: rgba(#333, 0.8);
        font-size: 18px;

        &.is-active {
          color: var(--color-white);
          font-weight: bold;
          background: linear-gradient(
            90deg,
            var(--color-primary),
            var(--color-second)
          );
        }

        &:last-of-type {
          margin-right: 0;
        }
      }
    }

    &-item {
      width: 100%;
      height: 478px;
      margin-top: 30px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/special-bg.png)
        no-repeat center / cover;
      padding: 0 30px;

      img {
        width: 530px;
        height: 378px;
        margin-right: 40px;
      }
    }

    &-title {
      padding-left: 36px;
      background-position: left center;
      background-size: 28px;
      background-repeat: no-repeat;
      margin-top: 30px;
    }

    &-description {
      margin-bottom: 35px;
      line-height: 28px;
    }

    $iconList: 'pc', 'wechat', 'community';

    @each $icon in $iconList {
      &-#{$icon} {
        background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/#{$icon}.png);
      }
    }
  }

  &__count {
    width: 560px;
    height: 28px;
    padding: 0 22px;
    border-radius: 4px;
    margin-top: 13px;
    margin-bottom: 20px;
  }

  &__point {
    display: block;
    margin-bottom: 18px;
    width: 380px;

    @include primary-point(10);

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  $count: 10, 15, 18, 20;

  @each $i in $count {
    &__mb-#{$i} {
      margin-bottom: $i + px;
    }
  }

  &__news {
    width: 440px;
    height: 493px;
    padding: 20px;
    background: linear-gradient(180deg, #d4e5ff, #ffffff);
    background-size: 100% 65px;
    background-repeat: no-repeat;
    background-color: var(--color-white);

    &-header {
      height: 74px;
      color: #2a4686;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/new-icon.png)
        no-repeat right 10px top / 74px;
    }

    &-tips {
      color: rgba(#2a4686, 0.8);
    }

    :deep() {
      .el-carousel__indicator {
        .el-carousel__button {
          width: 27px;
          height: 4px;
          background-color: var(--color-primary);
          border-radius: 2px;
        }
      }
    }
  }

  &__popular {
    width: 740px;
    height: 493px;
    margin-right: 20px;
    margin-bottom: 60px;

    &--container {
      padding-bottom: 30px;
    }

    &-header {
      height: 60px;
    }

    &-check {
      position: relative;
      width: 353px;
      flex: 50%;
      height: 100%;
      background-color: #d7e7ff;
      color: #2a4686;
      line-height: 60px;
      border-radius: 12px 12px 0 0;
    }

    & .active {
      color: var(--color-primary);
      font-weight: bold;
      background-color: var(--color-white);
      border-radius: 12px 12px 0 0;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        width: 65px;
        height: 60px;
        right: -64px;
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/wave.png)
          no-repeat right center / cover;
        z-index: 1;
      }
    }

    &-list {
      padding: 20px;
      height: 417px;
      display: none;
      align-content: flex-start;
    }

    .list-active {
      display: flex;
    }

    &-item {
      width: 100%;
      height: 35px;
      padding: 0 20px;
      border-radius: 4px;
      margin-bottom: 15px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }

  &__check {
    &-company {
      &.active {
        &::after {
          content: none;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0px;
          left: -57px;
          width: 65px;
          height: 60px;
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/wave.png)
            no-repeat right center / cover;
          z-index: 1;
          transform: rotateZ(180deg);
        }
      }
    }
  }

  &__link {
    flex: 1;
    margin-left: 15px;
  }

  &__company {
    &-item {
      width: 340px;
      height: 80px;
      padding: 0 20px;
      border: 1px solid var(--color-border);
      margin-right: 20px;
      margin-bottom: 20px;

      &:nth-of-type(2n) {
        margin-right: 0;
      }

      &:first-of-type {
        .youth__sort {
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/sort-1.png)
            no-repeat center / cover;
        }
      }

      &:nth-of-type(2) {
        .youth__sort {
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/sort-2.png)
            no-repeat center / cover;
        }
      }

      &:nth-of-type(3) {
        .youth__sort {
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/sort-3.png)
            no-repeat center / cover;
        }
      }
    }
  }

  &__info {
    div {
      margin-top: 8px;
    }
  }

  &__sort {
    flex: none;
    width: 29px;
    height: 40px;
    line-height: 33px;
    margin-right: 15px;

    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/sort-4.png)
      no-repeat center / cover;
  }

  &__case {
    &-item {
      margin-right: 20px;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  &__recommend {
    &-button {
      margin: 30px auto 60px auto;
    }

    &-talent {
      margin-right: 20px;
      box-shadow: none;

      &:hover {
        color: var(--color-white);
      }
    }
  }

  &__card {
    margin-right: 20px;
  }

  &__previous {
    flex: none;
    width: 28px;
    height: 28px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-previous-ghost.png)
      no-repeat center / cover;
    margin-right: 20px;
  }

  &__next {
    width: 28px;
    height: 100%;
    right: 0;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-next-ghost.png)
      no-repeat center / 28px;
    z-index: 1;
  }

  &__carousel {
    padding-left: 90px;
    &-item {
      margin-right: 30px;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  &__more {
    &-list {
      margin-left: 90px;
      width: 960px;
      flex: none;
    }

    &-card {
      width: 240px;
      height: 200px;
      margin-right: 50px;
      flex: none;

      img {
        width: 100%;
        height: 126px;
        border-radius: 12px 12px 0 0;
      }
    }

    &-title {
      height: 75px;
      border-radius: 0 0 12px 12px;
      padding: 15px;
    }

    &-text {
      @include ellipsis-lines(2, 21px);
    }

    &-announce {
      margin-top: 40px;
    }
  }

  &__common {
    &-point {
      @include primary-point(12);
    }
  }
}

.active-next {
  background-color: var(--background-primary);
  width: 38px;
  background-position: right;
  top: 0;
}

.point-primary {
  position: relative;
  padding-left: 12px;

  &::before {
    content: '';
    position: absolute;
    display: inline-block;
    flex: none;
    top: 50%;
    left: 0;
    width: 5px;
    height: 5px;
    background-color: var(--color-primary);
    border-radius: 50%;
    transform: translateY(-50%);
  }
}

.carousel--indicator {
  display: block;
  top: 50%;
  width: 28px;
  height: 28px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.carousel--previous {
  left: 20px;
  background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-previous-ghost.png);
}

.carousel--next {
  right: 20px;
  background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-next-ghost.png);
}

.more--next {
  top: 86px;
}

.more--previous {
  top: 86px;
  left: -20px;
}

.more-active {
  padding-left: 50px;
}

.mr-30 {
  margin-right: 30px;
}
</style>
