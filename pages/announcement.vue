<template>
  <div class="announcement__container">
    <app-menu :data="menuList" />

    <base-banner class="banner__wrapper" :data="banner" />

    <div
      id="重磅公告"
      class="heavy-announcement__wrapper background-color--primary"
    >
      <div class="view--width">
        <announcement-heavy :data="headlinesList" />
      </div>
    </div>

    <div id="推荐单位" class="recommend__wrapper background-color--primary">
      <div class="view--width">
        <announcement-recommend
          :data="{ bottomShowcaseList, topShowcaseList }"
        />
      </div>
    </div>

    <div id="全球求贤公告" class="recruit-announcement__wrapper">
      <div class="view--width">
        <base-title
          reverse
          prefix="全球"
          suffix="求贤公告"
          english="Global Wisdom Announcement"
        />

        <div v-loading="announcementLoading">
          <announcement-filter
            :data="announcementSearchList"
            @change="(data) => handleFilter(1, data)"
          />

          <template v-if="hasData(announcement.list)">
            <announcement-result :data="announcement.list" />

            <div class="flex--row flex-justify--center">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="announcement.count"
                v-model:current-page="announcementForm.page"
                :default-page-size="announcement.limit"
                @current-change="(page) => currentPageChange(page, 1)"
              />
            </div>
          </template>
          <template v-else>
            <div
              class="empty-container flex--column flex--center background-color--default border-radius"
            >
              <base-empty emptyType="公告" />
            </div>
          </template>
        </div>
      </div>
    </div>

    <div id="英才职位推荐" class="job__wrapper">
      <div class="view--width">
        <base-title
          reverse
          prefix="英才"
          suffix="职位推荐"
          english="Job Recommendation"
        />

        <div v-loading="jobLoading">
          <announcement-filter
            type="job"
            :data="jobSearchList"
            @change="(data) => handleFilter(2, data)"
          />

          <template v-if="hasData(job.list)">
            <announcement-job :data="job.list" />

            <div class="flex--row flex-justify--center">
              <el-pagination
                :class="jobRequestQuery ? '' : 'praise-pagination'"
                background
                layout="prev, pager, next"
                :total="job.count"
                v-model:current-page="jobForm.page"
                :default-page-size="job.limit"
                @current-change="(page) => currentPageChange(page, 2)"
              />
            </div>
          </template>
          <template v-else>
            <div
              class="empty-container flex--column flex--center background-color--default border-radius"
            >
              <base-empty emptyType="职位" />
            </div>
          </template>
        </div>
      </div>
    </div>

    <div id="人气关注排行" class="sort__wrapper background-color--primary">
      <div class="view--width">
        <base-title
          reverse
          prefix="人气"
          suffix="关注排行"
          english="Popular Following Ranking"
        />

        <div class="sort__content flex--row flex-justify--between">
          <announcement-hot-sort title="热门公告榜单" :themes-type="2">
            <base-hot-announcement
              :data="{ ...item, index }"
              v-for="(item, index) in hotAnnouncementList"
            />
          </announcement-hot-sort>

          <announcement-hot-sort title="热门职位榜单" :themes-type="1">
            <base-hot-job
              :data="{ ...item, index }"
              v-for="(item, index) in hotJobList"
            />
          </announcement-hot-sort>
        </div>
      </div>
    </div>

    <a
      v-if="footerShowcase.image"
      target="_blank"
      :href="footerShowcase.url"
      @click="addShowcase(footerShowcase.id, footerShowcase.number)"
      class="ad__wrapper flex--row"
    >
      <img
        class="ad__cover image--contain"
        :src="footerShowcase.image"
        :alt="footerShowcase.number"
      />
    </a>
  </div>
</template>

<script setup lang="ts">
import type { Reactive } from 'vue'

const fetchType = ref(1)

const announcementLoading = ref(false)
let announcementForm: Reactive<Announcement.Request> = reactive({
  cityId: '',
  companyType: '',
  jobCategoryId: '',
  majorId: '',
  refreshType: '',
  page: 1,
})

const jobLoading = ref(false)
let jobForm: Reactive<Announcement.Request> = reactive({
  cityId: '',
  companyType: '',
  jobCategoryId: '',
  majorId: '',
  refreshType: '',
  page: 1,
})
const jobRequestQuery = computed(() => {
  const { page, ...other } = jobForm
  return Object.values(other).some((value) => !!value)
})

const announcement: Reactive<
  Pick<Announcement.AnnouncementList, 'list' | 'count' | 'limit'>
> = reactive({
  list: [],
  count: 1,
  limit: 0,
})

const job: Reactive<Pick<Announcement.JobList, 'list' | 'count' | 'limit'>> =
  reactive({
    list: [],
    count: 1,
    limit: 0,
  })

const {
  announcementList,
  announcementSearchList,
  banner,
  footerShowcase,
  headlinesList,
  hotAnnouncementList,
  hotJobList,
  jobList,
  jobSearchList,
  topShowcaseList,
  bottomShowcaseList,
} = (await request({
  url: '/api/announcement/index',
})) as Announcement.Data

announcement.list = announcementList.list
announcement.count = announcementList.count
announcement.limit = announcementList.limit
job.list = jobList.list
job.count = jobList.count
job.limit = jobList.limit

const handleFilter = (type: number, query: object) => {
  fetchType.value = type
  const formData: { [key: string]: any; page: number } = { ...query, page: 1 }
  switch (type) {
    case 1:
      announcementForm = formData
      announcementLoading.value = true
      break
    case 2:
      Object.keys(formData).forEach(
        (key: string) => (jobForm[key] = formData[key]),
      )
      jobLoading.value = true
      break

    default:
      break
  }

  search()
}

const search = async () => {
  const type = fetchType.value
  type === 1 ? (announcementLoading.value = true) : (jobLoading.value = true)
  const query = type === 1 ? announcementForm : jobForm
  const api = `/api/announcement/${type === 1 ? 'get-announcement-list' : 'get-job-list'}`

  try {
    const { list, count, limit } = (await request({
      url: api,
      query,
    })) as Announcement.Data

    switch (type) {
      case 1:
        announcement.list = list
        announcement.count = count
        announcement.limit = limit
        announcementLoading.value = false
        break
      case 2:
        job.list = list
        job.count = count
        job.limit = limit
        jobLoading.value = false
        delay(handleJobPagination)
        break

      default:
        break
    }
  } catch {
    announcementLoading.value = false
    jobLoading.value = false
  }
}

const currentPageChange = async (page: number, type: number) => {
  // 改变type
  fetchType.value = type
  await search()
  scrollToTarget(type === 1 ? '全球求贤公告' : '英才职位推荐')
}

const handleJobPagination = () => {
  const pages = document.querySelectorAll('.praise-pagination .number')
  pages.forEach((el) => {
    const content = el.innerHTML
    const page = String(Number(content) - 1)
    el.setAttribute('data-page', page)
  })
}

const delay = (fn: () => void, delay = 300) => {
  setTimeout(fn, delay)
}

onMounted(() => {
  delay(handleJobPagination)
})

const menuList = computed(() => [
  {
    name: '重磅公告',
    visible:
      hasData(headlinesList.topList) || hasData(headlinesList.otherLines),
  },
  {
    name: '推荐单位',
    visible: hasData(topShowcaseList) || hasData(bottomShowcaseList),
  },
  {
    name: '全球求贤公告',
    visible: true,
  },
  {
    name: '英才职位推荐',
    visible: true,
  },
  {
    name: '人气关注排行',
    visible: hasData(hotAnnouncementList) || hasData(hotJobList),
  },
])
</script>

<style lang="scss" scoped>
.announcement__container {
  .heavy-announcement__wrapper {
    padding-top: 60px;
  }

  .recommend__wrapper {
    padding-top: 60px;
  }

  .recruit-announcement__wrapper {
    padding-bottom: 60px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/announcement/recruit-announcement-bg.png)
      no-repeat right bottom/610px 425px var(--background-primary);

    .announcement-list__container {
      margin-bottom: 30px;
    }
  }

  .job__wrapper {
    padding-bottom: 56px;

    .filter__wrapper {
      border: 1px solid var(--color-border-default);
    }

    .job-list__container {
      margin-bottom: 30px;
    }
  }

  .sort__wrapper {
    padding-bottom: 60px;

    .hot-announcement__container,
    .hot-job__container {
      margin-right: 10px;
      margin-bottom: 16px;
    }
  }

  .ad__wrapper {
    height: 100px;
  }

  .empty-container {
    padding: 70px 0;
    margin-top: 20px;
  }
}

.praise-pagination {
  :deep(.el-pager) {
    .number {
      $page: attr(data-page);
      font-size: 0;

      &::after {
        font-size: var(--el-pagination-font-size);
        content: $page;
      }

      &:first-child {
        &::after {
          content: '';
          width: 18px;
          height: 18px;
          background: url(/assets/images/icons/praise-primary.png) no-repeat
            center/100% 100%;
        }

        &.is-active {
          &::after {
            background: url(/assets/images/icons/praise-primary-active.png)
              no-repeat center/100% 100%;
          }
        }
      }
    }
  }
}
</style>
