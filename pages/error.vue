<template>
  <div class="error__container flex--column flex--center position--relative">
    <div class="error__content position--relative"></div>

    <div class="error__link text-align--center border-radius--8">
      <span
        class="display--block color--white font-size--18 cursor--pointer"
        @click="handleClick"
      >
        返回首页
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
const { routeList } = useConfig()

const handleClick = () => {
  navigateTo(routeList.home, { replace: true })
}
</script>

<style scoped lang="scss">
@keyframes animation-shake {
  0%,
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
}

.error {
  &__container {
    min-height: calc(100vh - var(--header-height) - 266px);

    &::before {
      content: '';
      position: absolute;
      width: 63px;
      height: 27px;
      top: 100px;
      right: 286px;
      background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/error/top.png')
        no-repeat center / cover;
    }

    &::after {
      content: '';
      position: absolute;
      width: 200px;
      height: 86px;
      bottom: 0;
      left: 44px;
      background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/error/bottom.png')
        no-repeat center / cover;
    }
  }

  &__content {
    margin: 0 auto;
    width: 801px;
    height: 582px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/error/background.png')
      no-repeat center / cover;

    &::before {
      content: '';
      position: absolute;
      width: 178px;
      height: 217px;
      top: 78px;
      left: 53px;
      background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/error/animation.png')
        no-repeat center / cover;
      animation: animation-shake 15s ease 0s infinite normal;
    }
  }

  &__link {
    margin: 30px auto;
    width: 140px;
    height: 35px;
    line-height: 35px;
    background-color: var(--color-primary);

    a:hover {
      color: var(--color-white);
    }
  }
}
</style>
