<template>
  <div class="company__container">
    <base-banner :data="banner" />

    <div class="company__content view--width">
      <base-advertise-list :data="{ bottomShowcaseList, topShowcaseList }" />

      <div v-loading="loading" id="filter">
        <company-filter
          :area-list="areaList"
          :company-type-list="companyTypeList"
          @update:params="handleParams"
        />

        <div class="company__result" v-if="hasData(company.list)">
          <div class="company__list flex--row flex--wrap">
            <company-card
              v-for="item in company.list"
              :key="item.id"
              :data="item"
            />
          </div>
          <el-pagination
            background
            class="company__page flex-justify--center"
            layout="prev, pager, next"
            :total="company.count"
            v-model:current-page="companyParams.page"
            @current-change="handleChangePage"
            :default-page-size="15"
          />
        </div>

        <div
          class="company__empty background-color--default border-radius flex--column flex-align--center"
          v-else
        >
          <base-empty :empty-type="'单位'" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Reactive } from 'vue'

const loading = ref(false)

const company: Reactive<Pick<Company.CompanyList, 'list' | 'count'>> = reactive(
  {
    list: [],
    count: 1,
  },
)

let companyParams: Reactive<Company.Request> = reactive({
  areaId: '',
  companyType: '',
  page: 1,
})

const {
  companyList,
  areaList,
  banner,
  bottomShowcaseList,
  topShowcaseList,
  companyTypeList,
} = (await request({
  url: '/api/company/index',
})) as Company.CompanyData

company.list = companyList.list
company.count = companyList.count

const handleSearch = async () => {
  loading.value = true
  const { list, count } = (await request({
    url: '/api/company/get-search-list',
    query: companyParams,
  })) as Company.CompanyList

  company.list = list
  company.count = count
  loading.value = false
}

const handleParams = (params: { area: []; type: []; page: 1 }) => {
  const { area, type, page } = params
  companyParams.areaId = area.join()
  companyParams.companyType = type.join()
  companyParams.page = page

  handleSearch()
}

const handleChangePage = (val: number) => {
  companyParams.page = val
  handleSearch()
  scrollToTarget('filter')
}
</script>

<style lang="scss" scoped>
.company {
  &__container {
    background-color: var(--background-primary);
    padding-bottom: 30px;
  }

  &__list {
    margin-top: 20px;
  }

  &__empty {
    padding: 70px 0;
    margin-top: 20px;
  }
}
</style>
