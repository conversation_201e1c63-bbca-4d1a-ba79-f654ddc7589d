<template>
  <div class="about__container">
    <app-menu :data="menu" />

    <base-banner :data="banner" />

    <div id="about" class="about__content background-color--default">
      <base-title
        prefix="关于"
        :suffix="title"
        english="About Gaocai Overseas"
        reverse
      />

      <div class="view--width">
        <div class="about__intro flex--column flex-justify--center">
          <div
            class="about__intro-name position--relative font-size--24 font-weight--bold"
          >
            {{ headerData.name }}
          </div>

          <div
            class="about__intro-desc font-size--16"
            v-html="headerData.desc"
          />
        </div>

        <ul class="about__intro-list flex--row flex-justify--between">
          <li
            v-for="(item, index) in headerData.items"
            :key="index"
            class="about__intro-item font-size--16 text-align--center border-radius--8"
          >
            {{ item }}
          </li>
        </ul>
      </div>
    </div>

    <div id="service" class="about__service">
      <base-title
        prefix="四大核心"
        suffix="产品服务"
        english="Four Core Products And Services"
        description="提供多元化一站式海外引才服务，助力单位高效引才"
        reverse
      />

      <div class="view--width">
        <div
          class="about__service-cell background-color--default border-radius overflow--hidden"
        >
          <div
            class="about__service-name flex--row font-size--28 font-weight--bold text-align--center"
          >
            <span
              v-for="item in serviceData"
              class="position--relative overflow--hidden"
              :key="item.id"
              :class="
                serviceCurrent === item.id ? 'about__service-name--active' : ''
              "
              :data-id="item.id"
              @mouseover="() => handleMouseOver(item)"
            >
              {{ item.name }}
            </span>
          </div>

          <div
            v-for="item in serviceData"
            :key="item.id"
            class="about__service-content display--none"
            :class="
              serviceCurrent === item.id ? 'about__service-content--active' : ''
            "
          >
            <div
              class="about__service-content-name font-size--24 font-weight--bold"
              :class="`about__service-content-name--${item.id}`"
            >
              {{ item.name }}
            </div>

            <div class="about__service-content-desc font-size--18">
              {{ item.desc }}
            </div>

            <div
              class="about__service-content-list flex--row flex--center"
              :class="`about__service-content-list--${item.id}`"
            >
              <div
                v-for="group in item.group"
                :key="group.name"
                class="about__service-content-list-item position--relative background-color--primary border-radius overflow--hidden"
              >
                <div
                  class="about__service-content-list-name font-size--20 font-weight--bold text-align--center"
                >
                  {{ group.name }}
                </div>

                <div
                  class="about__service-content-list-desc font-color--basic text-align--center"
                >
                  {{ group.desc }}
                </div>

                <div
                  class="about__service-content-list-over position--absolute flex--column color--white display--none"
                >
                  <span
                    class="font-size--20 font-weight--bold text-align--center"
                  >
                    {{ group.name }}
                  </span>

                  <ul>
                    <li v-for="it in group.items" :key="it">{{ it }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="advantage" class="about__advantage">
      <base-title
        prefix="服务"
        suffix="优势"
        english="Service Advantages"
        description="全球化资源 | 精细化邀约 | 全流程服务 | 高标准交付"
        reverse
      />

      <div class="view--width">
        <div
          class="about__advantage-title flex--row flex-align--center font-size--30 font-weight--bold"
          data-id="01"
        >
          全球化资源 触达世界顶尖名校菁英
        </div>

        <div
          class="about__advantage-content--1 about__advantage-content position--relative background-color--default border-radius"
        >
          <ul>
            <li class="position--relative font-size--18 font-weight--bold">
              全球<span class="color--primary font-size--24"> 130+ </span
              >海外引才合作站分布
            </li>
            <li class="position--relative font-size--18 font-weight--bold">
              覆盖
              <span class="color--primary font-size--24"> 20个 </span
              >高等教育水平领先的国家和地区
            </li>
            <li class="position--relative font-size--18 font-weight--bold">
              <span class="color--primary font-size--24"> 超百家 </span
              >海（境）外高校合作资源
            </li>
          </ul>

          <p class="font-color--basic">
            包含QS前100海外顶尖高校官方学联、海外高等教育发达地区官方学联、海外高层次学者组织机构/协会、海外留学生媒体平台等
          </p>
        </div>

        <div
          class="about__advantage-title flex--row flex-align--center font-size--30 font-weight--bold"
          data-id="02"
        >
          多维度进行人才精细化邀约
        </div>

        <div
          class="about__advantage-content--2 about__advantage-content flex--row"
        >
          <div
            class="about__border flex--column flex-justify--center position--relative border-radius"
          >
            <span class="font-size--24 font-weight--bold">全球渠道发动</span>
            <p>
              官网、小程序、H5、微信公众号、社群、高层次人才库、海外学者EDM推广、海外学联公众号、海外学联社群
            </p>
          </div>

          <div
            class="about__border flex--column flex-justify--center position--relative border-radius"
          >
            <span class="font-size--24 font-weight--bold">意向人才对接</span>
            <p>
              定制个性化报名表、创建专属社群、倒计时海报提醒、微信1v1、邮件通知等
            </p>
          </div>
        </div>

        <div
          class="about__advantage-title flex--row flex-align--center font-size--30 font-weight--bold"
          data-id="03"
        >
          全流程海外引才服务保障
        </div>

        <div
          class="about__advantage-process about__advantage-content border-radius overflow--hidden"
        ></div>

        <div
          class="about__advantage-title flex--row flex-align--center font-size--30 font-weight--bold"
          data-id="04"
        >
          以高标准交付沉淀好口碑
        </div>

        <div
          class="about__advantage-content--4 about__advantage-content flex--row font-size--16"
        >
          <ul
            class="about__border box--1 flex--column position--relative border-radius"
          >
            <li>
              获批
              <span class="position--relative font-size--18 font-weight--bold"
                >广东省科协海智计划广州高才信息科技有限公司工作站</span
              >
            </li>
            <li>
              荣获
              <span class="position--relative font-size--18 font-weight--bold"
                >2024年度人力资源服务质量品牌优秀奖</span
              >
            </li>
            <li>
              荣获
              <span class="position--relative font-size--18 font-weight--bold"
                >2023及2024海交会最佳组织奖</span
              >
            </li>
            <li>
              荣获
              <span class="position--relative font-size--18 font-weight--bold"
                >荣获香港人才政策支持奖</span
              >
            </li>
          </ul>

          <div>
            <ul
              class="about__border box--2 flex--column position--relative border-radius"
            >
              <li>
                <span class="color--primary font-size--20 font-weight--bold"
                  >100+</span
                >
                海外引才合作单位
              </li>
              <li>
                <span class="color--primary font-size--20 font-weight--bold"
                  >100+</span
                >
                场海（境）外落地招聘宣讲活动
              </li>
              <li>
                <span class="color--primary font-size--20 font-weight--bold"
                  >16个</span
                >
                落地国家和地区：中国香港、日本、韩国、新加坡、俄罗斯、英国、法国、德国、瑞典、瑞士、美国、澳大利亚、新西兰、荷兰、马来西亚、丹麦
              </li>
            </ul>

            <ul
              class="about__border box--3 flex--column position--relative border-radius"
            >
              <li>
                <span class="color--primary font-size--20 font-weight--bold"
                  >5800+</span
                >
                名海（境）外到会博士，含：博士后及在职学者1300多人&海优层次人才超500人
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div id="case" class="about__case background-color--default">
      <base-title
        prefix="合作"
        suffix="案例"
        english="Collaboration Cases"
        reverse
      />

      <div class="view--width position--relative">
        <el-carousel
          ref="carouselRef"
          trigger="click"
          height="450px"
          arrow="never"
          :autoplay="false"
          indicator-position="none"
        >
          <el-carousel-item
            v-for="(group, index) in caseDataGroup"
            :key="index"
          >
            <div class="about__case-carousel flex--row flex-align--center">
              <div
                class="about__case-carousel-item"
                v-for="(item, index) in group"
                :key="index"
              >
                <base-collaboration-case :data="item" />
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>

        <template v-if="caseDataGroup.length > 1">
          <span
            class="carousel--previous carousel--indicator position--absolute cursor--pointer"
            @click="() => handleCarousel(-1)"
          />

          <span
            class="carousel--next carousel--indicator position--absolute cursor--pointer"
            @click="() => handleCarousel(1)"
          />
        </template>
      </div>

      <div
        class="about__case-cooperation position--relative overflow--hidden"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { title } = useConfig()

const {
  banner,

  cooperationCaseList,

  headerData = {
    name: '搭建海外招才引智平台  助力海外智力为国服务',
    desc: '“高才海外HiTalentGlobal”是高才科技旗下专注于海（境）外引才服务的平台。高才科技（全称“广州高才信息科技有限公司”），致力于为国内高等院校、科研院所、医疗机构、企事业单位等用人单位提供海内外高层次人才招聘综合服务，由高才科技运营的“高校人才网”深耕行业18载，已积累了超百万硕博等高层次人才资源。<br /><br />为响应国家人才强国战略，顺应人才跨国流动与交流的需求，高才科技持续开拓与探索海（境）外引才服务，推出了“高才海外HiTalentGlobal”品牌。为国内用人单位提供出海引才服务、归国活动服务、线上宣传服务及人选对接服务等四大产品服务，助力用人单位高效组建国际化高层次人才梯队。',
    items: [
      '专业聚焦海外高层次人才',
      '超百家世界顶尖高校资源',
      '丰富精准的全球渠道宣传',
      '多样精细的海外引才服务',
    ],
  },

  serviceData = [
    {
      id: '01',
      name: '出海引才服务',
      desc: '依托全球超百家海外合作站点支持，提供行程策划、活动宣传、人才邀约&运营、海外学联合作、活动组织等服务选择，满足用人单位活动宣传、组团招聘、专场招聘等多种海（境）外引才场景及需求，为单位出海引才增效赋能。',
      group: [
        {
          name: '海外组团招聘',
          desc: '打造“国内高校、科研院所招才引智海（境）外名校行”品牌活动',
          items: [
            '以组团形式开展招聘宣讲会',
            '助揽QS前100名校博士',
            '多家知名单位聚集更多关注',
            '全流程服务促引才省时省力',
          ],
        },
        {
          name: '海外专场活动',
          desc: '已服务国内100多所中科院、国家级实验室、985/211/双一流等知名单位',
          items: [
            '专场定制化服务满足个性需求',
            '提供全球顶尖学府资源支持',
            '雇主品牌包装增强人才关注',
            '多渠道多形式保障活动成效',
          ],
        },
        {
          name: '海外活动宣传',
          desc: '全球化渠道宣传，助力海外活动增强人气，扩大影响力',
          items: [
            '超百家海外学联渠道匹配',
            '高才官网多入口引流曝光',
            '公众号&海外社群精准推送',
            '平台高层次人才库精选覆盖',
          ],
        },
      ],
    },
    {
      id: '02',
      name: '归国活动服务',
      desc: '为用人单位提供论坛落地服务、论坛目标人才邀约服务及活动线上直播，通过全球化宣传、线上线下直面交流、人才接洽及引导等环节助力用人单位更生动且深入地展示自身平台优势和引才政策，吸引更多有意向回国发展的高层次留学人才了解和加入。',
      group: [
        {
          name: '归国活动承办',
          desc: '为用人单位提供全流程专业化的国际学者论坛落地服务',
          items: [
            '海内外高流量宣传渠道支持',
            '丰富的全球高层次人才资源',
            '成熟的项目团队高效化运营',
            '超百场海内外活动经验保障',
          ],
        },
        {
          name: '目标人才邀约',
          desc: '多渠道为单位的论坛活动提供符合要求的学者匹配及邀约',
          items: [
            '高才海外渠道广泛发动',
            '目标学联渠道精准覆盖',
            '整合人才信息筛选推荐',
            '多形式多频次人才对接',
          ],
        },
        {
          name: '活动线上直播',
          desc: '为用人单位定制海外直播专场活动，实现全球高层次人才对接',
          items: [
            '创建专属直播间聚流',
            '提供宣讲一站式指引',
            '搭建专属社群定向运营',
            '多入口为直播曝光导流',
          ],
        },
      ],
    },
    {
      id: '03',
      name: '线上宣传服务',
      desc: '平台拥有丰富且高流量的媒体矩阵，聚集了海量来自世界各大知名高校的硕博人才，能为用人单位匹配网站、微信公众号、社群、海外学联等渠道进行精准化高曝光宣传，为用人单位招聘及引才活动引流。',
      group: [
        {
          name: '网站平台宣传',
          desc: '国内访问量、信息量排名前列的高层次人才招聘宣传网站',
          items: [
            '日页面浏览量近80万',
            '日访客量近10万人次',
            '年均海外访客超65万',
            '高层次人才注册量100万+',
          ],
        },
        {
          name: '新媒体渠道宣传',
          desc: '高校人才网精细化运营各类微信公众号，覆盖百万高层次人才推广',
          items: [
            '高才-高校人才网180万+粉丝',
            '高才海外、高才博士后定向推广',
            '助力单位需求短时间快速传播',
            '多种增值服务加持扩大宣传',
          ],
        },
        {
          name: '人才社群宣传',
          desc: '依托380+社群18万+硕博用户及好友，提供社群精准定点推广服务',
          items: [
            '按照学科/地区需求匹配宣推',
            '41个海归社群提供定向推广',
            '7400+海归博士群人数',
            '社群/朋友圈/1v1邀约精准发动',
          ],
        },
        {
          name: '海外渠道宣传',
          desc: '超百家紧密合作的海（境）外学联渠道，触达20个高等教育水平领先国家和地区',
          items: [
            '海外学联公众号精准推广',
            '触达目标地区及高校人才',
            '海外学联社群多频次覆盖',
            '深入目标群体提高人才关注',
          ],
        },
      ],
    },
    {
      id: '04',
      name: '人选对接服务',
      desc: '依托平台多元化的宣传渠道、40万+高层次人才库、资深的猎头经验以及成熟的人选寻访能力，将为用人单位提供强有力的人才资源支持和高效精细的人才寻猎服务，帮助单位快速获取目标人才。',
      group: [
        {
          name: '人才猎头服务',
          desc: '提供精细化猎头服务，为用人单位精准寻猎海（境）外优质人才',
          items: [
            '资深猎头提供1v1服务对接',
            '以结果为导向定制招聘策略',
            '内外部渠道多维寻访筛选人才',
            '实施紧密细化的沟通&执行管理',
          ],
        },
        {
          name: '项目人才推荐',
          desc: '为有海外优青项目等引才需求的用人单位提供人才推荐服务',
          items: [
            '匹配线上线下招聘宣传资源',
            '全方位进行人才发动及邀约',
            '意向人才报名管理及筛选',
            '阶段性进行人才数据整理及汇报',
          ],
        },
      ],
    },
  ],
} = (await request({ url: '/api/about/index' })) as API.About

const serviceCurrent = ref('01')

const caseDataGroup = computed(() => {
  const group = cooperationCaseList.reduce(
    (
      result: API.About['cooperationCaseList'],
      current: API.About['cooperationCaseList'],
      index: number,
    ) => {
      if (index % 3 === 0) {
        result.push([current])
      } else {
        result.at(-1).push(current)
      }
      return result
    },
    [],
  )

  return group
})

const carouselRef = ref()

const handleMouseOver = (data: (typeof serviceData)[number]) =>
  (serviceCurrent.value = data.id)

const handleCarousel = (value: number) => {
  const action = value > 0 ? 'next' : 'prev'

  carouselRef.value[action]()
}

const menu = [
  { name: '关于我们', value: 'about', visible: true },
  { name: '产品服务', value: 'service', visible: true },
  { name: '服务优势', value: 'advantage', visible: true },
  { name: '合作案例', value: 'case', visible: true },
]
</script>

<style scoped lang="scss">
@keyframes animation-move {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

.about {
  &__border {
    box-shadow: 0 0 0 1px #99b8ff;
  }

  &__intro {
    padding-right: 580px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/header-image.png)
      no-repeat right center / 535px 423px;
    margin-bottom: 40px;
    height: 423px;

    &-name {
      padding-left: 20px;
      margin-bottom: 40px;
      z-index: 1;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 44px;
        height: 44px;
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/header-name.png)
          no-repeat left bottom / contain;
        z-index: -1;
      }
    }

    &-desc {
      line-height: 2;
    }

    &-item {
      margin-bottom: 60px;
      padding-left: 40px;
      width: 285px;
      height: 70px;
      line-height: 70px;
      background-color: var(--background-primary);
      background-repeat: no-repeat;
      background-position: 30px center;
      background-size: 36px 36px;

      @for $i from 1 through 4 {
        &:nth-of-type(#{$i}) {
          background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/header-icon-#{$i}.png);
        }
      }
    }
  }

  &__service {
    padding-bottom: 60px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/background-service.png)
      no-repeat center / cover;

    &-name {
      height: 80px;
      line-height: 80px;
      background-color: rgba(#f8f8f8, 0.8);

      &--active {
        color: var(--color-primary);
        background-color: var(--color-white);
        border-radius: 12px 12px 0 0;

        &::before {
          display: none;
        }

        & + span {
          &::before {
            display: none;
          }
        }
      }

      span {
        width: 25%;
        z-index: 1;

        &:first-of-type {
          &::before {
            display: none;
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: 1px;
          height: 40px;
          background-color: rgba(#c9cdd2, 0.8);
          transform: translateY(-50%);
        }

        &::after {
          content: attr(data-id);
          position: absolute;
          right: 24px;
          bottom: -24px;
          color: rgba($color-primary, 0.1);
          font-size: 93px;
          font-weight: bold;
          z-index: -1;
        }
      }
    }

    &-content {
      padding: 40px 30px;

      &--active {
        display: block;
      }

      &-name {
        margin-bottom: 10px;
        padding-left: 64px;
        line-height: 46px;
        background-repeat: no-repeat;
        background-position: left center;
        background-size: 46px 46px;

        @for $i from 1 through 4 {
          &--0#{$i} {
            background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-name-#{$i}.png);
          }
        }
      }

      &-desc {
        line-height: 2;
      }

      &-list {
        margin-top: 30px;

        $alias: &;

        &-item {
          margin-right: 30px;
          padding: 148px 20px 0;
          width: 260px;
          height: 290px;
          background-repeat: no-repeat;
          background-position: center 48px;
          background-size: 70px 70px;

          &:last-of-type {
            margin-right: 0;
          }

          &:hover {
            #{$alias}-over {
              display: flex;
            }
          }
        }

        &--01 {
          @for $i from 1 through 3 {
            #{$alias}-item:nth-of-type(#{$i}) {
              background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-item-1-#{$i}.png);
            }
          }
        }

        &--02 {
          @for $i from 1 through 3 {
            #{$alias}-item:nth-of-type(#{$i}) {
              background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-item-2-#{$i}.png);
            }
          }
        }

        &--03 {
          @for $i from 1 through 4 {
            #{$alias}-item:nth-of-type(#{$i}) {
              background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-item-3-#{$i}.png);
            }
          }
        }

        &--04 {
          @for $i from 1 through 2 {
            #{$alias}-item:nth-of-type(#{$i}) {
              background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-item-4-#{$i}.png);
            }
          }
        }

        &-name {
          margin-bottom: 20px;
        }

        &-over {
          top: 0;
          left: 0;
          padding: 40px 20px;
          width: 100%;
          height: 100%;
          background:
            url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/service-item.png)
              no-repeat center bottom / 260px 132px,
            linear-gradient(4deg, var(--color-second), var(--color-primary));

          span {
            margin-bottom: 20px;
          }

          ul {
            list-style: inside;
            list-style-type: disc;
          }

          li {
            line-height: 34px;
          }
        }
      }
    }
  }

  &__advantage {
    padding-bottom: 20px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/background-advantage.png)
      no-repeat center / cover;

    &-title {
      margin-bottom: 40px;

      &::before {
        content: attr(data-id);
        margin-right: 20px;
        width: 48px;
        height: 48px;
        color: var(--color-white);
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        line-height: 48px;
        background-color: var(--color-primary);
        border-radius: 10px;
        transform: skewX(-4deg);
      }
    }

    &-content {
      margin-bottom: 60px;

      &--1 {
        margin-top: 60px;
        padding: 6px 795px 38px 30px;

        &::after {
          content: '';
          position: absolute;
          top: -42px;
          right: 0;
          width: 772px;
          height: 329px;
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-1.png)
            no-repeat center / contain;
        }

        li {
          padding-left: 16px;
          line-height: 62px;
          border-bottom: 1px solid var(--color-border);

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 5px;
            height: 5px;
            background-color: var(--color-primary);
            border-radius: 50%;
            transform: translateY(-50%);
          }

          &:last-of-type {
            border-bottom: none;
          }
        }

        p {
          padding-left: 16px;
          line-height: 21px;
        }
      }

      &--2 {
        .flex--column {
          margin-right: 20px;
          padding: 0 200px 0 30px;
          width: 590px;
          height: 200px;
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-2.png)
            no-repeat center / cover;

          &:last-of-type {
            margin-right: 0;
          }

          &::after {
            content: '';
            position: absolute;
            top: 9px;
            right: 0;
            width: 205px;
            height: 182px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
          }

          @for $i from 1 through 2 {
            &:nth-of-type(#{$i})::after {
              background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-2-#{$i}.png);
            }
          }
        }

        span {
          margin-bottom: 20px;
        }

        p {
          line-height: 19px;
        }
      }

      &--4 {
        .box--1 {
          margin-right: 20px;
          padding-top: 58px;
          padding-left: 40px;
          width: 590px;
          height: 290px;
          background: linear-gradient(90deg, #ffffff, #dee9fd, #dee9fd);

          &::before {
            content: '';
            position: absolute;
            top: 41px;
            right: 0;
            width: 104px;
            height: 174px;
            background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-4.png)
              no-repeat center / contain;
          }

          &::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 221px;
            height: 133px;
            background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-4-1.png)
              no-repeat center / contain;
          }

          li {
            margin-bottom: 26px;
          }

          span {
            z-index: 1;

            &::after {
              content: '';
              position: absolute;
              top: 15px;
              left: 0;
              width: 100%;
              height: 6px;
              background: linear-gradient(90deg, #296aff, #deeafd);
              border-radius: 3px;
              z-index: -1;
            }
          }
        }

        .box--2 {
          margin-bottom: 12px;
          padding: 16px 154px 0 30px;
          width: 590px;
          height: 178px;
          background:
            url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-4-2.png)
              no-repeat right center / 225px 178px,
            linear-gradient(90deg, #ffffff, #dee9fd);

          li {
            margin-bottom: 12px;
          }
        }

        .box--3 {
          padding: 26px 194px 26px 30px;
          width: 590px;
          height: 100px;
          background:
            url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/advantage-item-4-3.png)
              no-repeat right center / 226px 101px,
            linear-gradient(90deg, #ffffff, #dee9fd);
        }
      }
    }

    &-process {
      height: 414px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/background-process.png)
        no-repeat center / contain;
    }
  }

  &__case {
    &-carousel {
      height: 450px;

      &-item {
        margin-right: 20px;

        &:last-of-type {
          margin-right: 0;
        }
      }
    }

    .carousel--indicator {
      top: 50%;
      width: 28px;
      height: 28px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }

    .carousel--previous {
      left: -48px;
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-previous-ghost.png);
    }

    .carousel--next {
      right: -48px;
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-next-ghost.png);
    }

    &-cooperation {
      margin-top: 60px;
      height: 289px;

      &::before {
        content: '';
        position: absolute;
        width: 6058px;
        height: 100%;
        background:
          url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/cooperation.png)
            no-repeat left center / auto 100%,
          url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/cooperation.png)
            no-repeat 2000px center / auto 100%,
          url(https://img.gaoxiaojob.com/uploads/haiwai/images/about/cooperation.png)
            no-repeat 4000px center / auto 100%;
        animation: animation-move 100s linear infinite normal none;
      }
    }
  }
}
</style>
