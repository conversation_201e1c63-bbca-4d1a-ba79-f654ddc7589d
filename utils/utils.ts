import { useCookie } from '#app'

export function booleanString(str: string): boolean {
  return str === '1'
}

export function isIndexPage(path: string): boolean {
  return path === '/'
}

export function hasData(data: Base.Any): boolean {
  return data && Object.keys(data).length > 0
}

export function toLink(url: string, target: Base.Target = '_blank') {
  window.open(url, target)
}

export function getScrollTop(target: HTMLElement = document.documentElement) {
  return target?.scrollTop ?? 0
}

export function getWindowRect() {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  }
}

export function smoothScrollTo(top: number = 0) {
  window.scrollTo({ top, behavior: 'smooth' })
}

export function scrollToTarget(target: string) {
  const headerDom = document.querySelector('#header') as HTMLDivElement
  const targetDom = document.querySelector(`#${target}`) as HTMLDivElement

  if (headerDom && targetDom) {
    const { offsetHeight } = headerDom
    const { offsetTop } = targetDom

    smoothScrollTo(offsetTop - offsetHeight)
  }
}

export function addShowcase(id: string, number: string) {
  const config = useRuntimeConfig()
  const cookies = useCookie('gaoxiaojob').value || ''
  const token = useCookie('gaoxiaojobPositionToken').value
  const urlBase = config.public.urlBase
  let _maqData = {}
  let baseUrl = urlBase + '/1.gif'
  let args = ''
  for (var i in _maqData) {
    if (args != '') {
      args += '&'
    }
    args += i + '=' + encodeURIComponent(_maqData[i])
  }
  args += '&number=' + number
  args += '&id=' + id
  args += '&token=' + token
  args += '&cookies=' + cookies
  let img = new Image()

  img.src = baseUrl + '?' + args
}
