export default async function request(options: Request.Options) {
  const { url, ...rest } = options

  const response = await $fetch(url, {
    ...rest,
  })

  const { data, message, status } = response as Request.Response

  return new Promise((resolve, reject) => {
    if (status === 200) {
      resolve(data)
    } else {
      reject(response)

      message && ElMessage.error(message)
    }
  })
}
