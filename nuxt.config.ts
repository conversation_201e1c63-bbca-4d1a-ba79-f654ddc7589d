const baseDomainOptions = {
  release: 'gaoxiaojob.com',
  test: 'test.gcjob.jugaocai.com',
  dev: 'dev.gcjob.jugaocai.com',
  pre: 'pre.gcjob.jugaocai.com',
  gray: 'gray.gcjob.jugaocai.com',
  dong: 'gaoxiaojob.dong',
}

const { NODE_ENV_PROXY = 'release' } = process.env

const domain =
  baseDomainOptions[NODE_ENV_PROXY as keyof typeof baseDomainOptions]

let apiBase = `https://api.haiwai.${domain}`

if (NODE_ENV_PROXY === 'dong') {
  apiBase = `http://api.haiwai.${domain}`
}

let urlBase = `//${domain}`

let script: any = []
if (NODE_ENV_PROXY === 'release') {
  urlBase = `//www.${domain}`
  script = [
    { src: 'https://hm.baidu.com/hm.js?2154c8c4b015b6234a9185ba57989df7' },
  ]
}

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',

      meta: [
        {
          name: 'applicable-device',
          content: 'pc',
        },
      ],

      script: script,

      htmlAttrs: {
        lang: 'zh-CN',
      },
    },

    rootAttrs: {
      id: 'app',
    },

    buildAssetsDir: '/static/',
  },

  css: ['assets/themes/common.scss'],

  modules: ['@element-plus/nuxt'],

  runtimeConfig: {
    public: {
      apiBase,
      urlBase,
      env: NODE_ENV_PROXY,
    },
  },

  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
          @use "assets/themes/variables.scss" as *;
          @use "assets/themes/mixins.scss" as *;
          `,
        },
      },
    },
  },

  elementPlus: {
    icon: 'ElIcon',
    importStyle: 'scss',
  },
})
