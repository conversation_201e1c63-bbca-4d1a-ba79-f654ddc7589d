import { type UseSeoMetaInput } from '@unhead/vue'
import { type H3Event } from 'h3'

export default defineNuxtRouteMiddleware(async (to) => {
  const headers = useRequestHeaders()

  const event = useRequestEvent() as H3Event

  const config = useConfig()

  const { name, path, query } = to

  let { meta } = to

  const url = `/api/common/tdk${isIndexPage(path) ? '' : path}`

  try {
    const tdk = (await request({ url, query })) as API.CommonTDK

    meta = { ...meta, ...tdk }
  } catch {}

  if (!isIndexPage(path)) {
    meta.title = `${meta.title}-${config.title}`
  }

  if (/Mobile/.test(headers['user-agent'])) {
    useHead({
      meta: [
        {
          name: 'viewport',
          content:
            'width=device-width, initial-scale=0.3, minimum-scale=0.3, maximum-scale=1.0, user-scalable=yes',
        },
      ],
    })
  }

  if (name === 'error') {
    setResponseStatus(event, 404)
  }

  useSeoMeta(meta as UseSeoMetaInput)
})
