<template>
  <div
    class="announcement-list__container flex--row flex-justify--between flex--wrap"
  >
    <base-announcement :data="item" v-for="item in data" />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.AnnouncementPropData,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.announcement-list {
  &__container {
    .announcement__container {
      margin-bottom: 20px;
    }
  }
}
</style>
