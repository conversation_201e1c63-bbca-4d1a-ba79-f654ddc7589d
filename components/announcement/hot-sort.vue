<template>
  <div
    class="hot-sort__container border-radius flex--column position--relative"
  >
    <div
      class="hot-sort__header font-weight--bold text-align--center position--absolute position--x-center"
      :class="`hot-sort__header--${classSuffix}`"
    >
      {{ title }}
    </div>

    <div class="hot-sort__list flex--1 overflow--auto scrollbar--primary">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '热门职位榜单',
  },
  themesType: {
    type: Number,
    default: 1,
  },
})

const classSuffix = computed(() => {
  let suffix = ''
  switch (props.themesType) {
    case 1:
      suffix = 'job'
      break
    case 2:
      suffix = 'announcement'
      break
    default:
      suffix = 'job'
      break
  }
  return suffix
})
</script>

<style scoped lang="scss">
.hot-sort {
  &__container {
    width: calc((100% - 20px) / 2);
    max-height: 694px;
    background: linear-gradient(90deg, #e6eeff, #f5fdff);
    padding: 54px 10px 20px 20px;
    border: 2px solid var(--color-border-default);
  }

  &__header {
    width: 415px;
    line-height: 52px;
    top: -22px;
    font-size: 26px;
    color: var(--color-white);

    &--job {
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/announcement/hot-job.png)
        no-repeat left/100% 100%;
    }

    &--announcement {
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/announcement/hot-announcement.png)
        no-repeat left/100% 100%;
    }
  }

  &__list {
    padding: 1px;
  }
}
</style>
