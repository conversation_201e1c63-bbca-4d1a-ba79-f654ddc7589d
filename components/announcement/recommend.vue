<template>
  <div
    class="recommend__title flex--row flex-justify--between flex-align--center"
  >
    <base-title title="推荐单位" english="Recommended Unit" />

    <nuxt-link class="recommend__more font-bold" :to="routeList.company">
      More
    </nuxt-link>
  </div>

  <base-advertise-list :data="data" />
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseAdvertise,
    default: () => {},
  },
})
const { routeList } = useConfig()
</script>

<style scoped lang="scss">
.recommend {
  &__title {
    padding-bottom: 10px;
  }

  &__more {
    padding: 0 20px;
    line-height: 30px;
    border-radius: 16px;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
  }
}

.advertise__container {
  margin: 0;
}
</style>
