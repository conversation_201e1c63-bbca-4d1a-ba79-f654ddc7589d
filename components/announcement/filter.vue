<template>
  <div
    class="filter__wrapper border-radius flex--row flex-align--center flex-justify--between background-color--default"
  >
    <div class="filter__condition flex--row flex-align--center">
      <span class="label"
        >筛选{{ type === 'announcement' ? '公告' : '职位' }}：</span
      >
      <el-form :model="formData" ref="form" inline label-width="0px">
        <el-form-item prop="cityId">
          <base-select
            class="filter__select"
            filterable
            v-model="formData.cityId"
            placeholder="地区"
            :options="data.cityId"
            @change="handleChange"
          ></base-select>
        </el-form-item>

        <el-form-item prop="majorId">
          <base-select
            class="filter__select"
            filterable
            v-model="formData.majorId"
            placeholder="需求专业"
            :options="data.majorId"
            @change="handleChange"
          ></base-select>
        </el-form-item>

        <el-form-item prop="jobCategoryId">
          <base-select
            class="filter__select"
            filterable
            v-model="formData.jobCategoryId"
            placeholder="职位类型"
            :options="data.jobCategoryId"
            @change="handleChange"
          ></base-select>
        </el-form-item>

        <el-form-item prop="companyType">
          <base-select
            class="filter__select"
            filterable
            v-model="formData.companyType"
            placeholder="单位类型"
            :options="data.companyType"
            @change="handleChange"
          ></base-select>
        </el-form-item>

        <el-form-item prop="refreshType">
          <base-select
            class="filter__select"
            v-model="formData.refreshType"
            placeholder="发布时间"
            :options="data.refreshType"
            @change="handleChange"
          ></base-select>
        </el-form-item>
      </el-form>
    </div>
    <div
      class="filter__clear font-color--basic cursor--pointer"
      @click="handleClear"
    >
      清空筛选条件
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String as Components.AnnouncementJobType,
    default: 'announcement',
  },
  data: {
    type: Object as Components.AnnouncementFilterProp,
    default: {
      cityId: [],
      companyType: [],
      jobCategoryId: [],
      majorId: [],
      refreshType: [],
    },
  },
})

const emit = defineEmits(['change'])

const form = ref()
const formData = reactive({
  cityId: '',
  companyType: '',
  jobCategoryId: '',
  majorId: '',
  refreshType: '',
})

const handleChange = () => {
  emit('change', formData)
}

const handleClear = () => {
  form.value.resetFields()
  emit('change', formData)
}
</script>

<style scoped lang="scss">
.filter {
  &__wrapper {
    padding: 12px 20px 12px 18px;
    margin-bottom: 20px;

    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
    }
  }

  &__select {
    width: 124px;
    margin-right: 16px;
  }

  &__clear {
    padding-left: 20px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/delete-default.png)
      no-repeat left/16px 16px;
  }
}
</style>
