<template>
  <div class="announcement__container border-radius background-color--default">
    <nuxt-link
      :to="top.url"
      class="announcement__title font-weight--bold text-align--center"
      target="_blank"
      :title="top.title"
    >
      {{ top.title }}
    </nuxt-link>
    <div class="announcement__list flex--row flex-justify--between">
      <nuxt-link
        :key="item.id"
        v-for="item in list"
        class="announcement__item font-16"
        :to="item.url"
        :title="item.title"
        target="_blank"
        >{{ item.title }}</nuxt-link
      >
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.AnnouncementHeavyProp,
    default: {
      topList: {},
      otherLines: [],
    },
  },
})

const top = computed(() => props.data.topList)
const list = computed(() => props.data.otherLines)
</script>

<style scoped lang="scss">
.announcement {
  &__container {
    border: 1px solid var(--color-border-default);
    padding: 70px 50px 0;
  }

  &__title {
    @include ellipsis-lines(1, 70px, 22px);
    margin-top: -90px;
    margin-bottom: 20px;

    width: 1105px;
    padding-left: 230px;
    padding-right: 230px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/announcement/announcement-bg.png)
      no-repeat left top/contain;
  }

  &__list {
    flex-wrap: wrap;
  }

  &__item {
    @include ellipsis-lines(1, 1, 16px);

    margin-bottom: 20px;
    width: 479px;
    padding-left: 11px;
    position: relative;

    &:nth-child(2n) {
      width: 544px;
    }

    @include primary-point(8);
  }
}
</style>
