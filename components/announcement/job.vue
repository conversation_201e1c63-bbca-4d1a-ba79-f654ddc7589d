<template>
  <div class="job-list__container flex--row flex--wrap">
    <base-job class="job__list" :data="item" v-for="item in data" />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.JobPropData,
    default: [],
  },
})
</script>

<style scoped lang="scss">
.job-list {
  &__container {
    .job__container {
      margin-bottom: 20px;
      margin-right: 20px;

      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}
</style>
