<template>
  <ul class="talent__list flex--row flex--wrap">
    <li
      v-for="item in data"
      :key="item.id"
      class="talent__item flex--column position--relative background-color--default border-radius animation-mouseover--border"
    >
      <base-tag
        class="position--absolute"
        style="top: 0; left: 0"
        type="warning"
        :text="item.tag"
      />

      <a :href="item.url" target="_blank" :title="item.title" :rel="item.rel">
        <img
          class="talent__item-image image--cover"
          :src="item.image"
          :alt="item.title"
        />

        <img
          class="talent__item-logo position--absolute position--x-center image--logo image--contain"
          :src="item.logo"
          :alt="item.title"
        />

        <div class="talent__item-title font-weight--bold">
          {{ item.title }}
        </div>
      </a>

      <div
        class="talent__item-info flex--row flex-justify--between flex-align--center"
      >
        <div class="talent__item-box flex--column">
          <span class="talent__item-date font-color--basic overflow--ellipsis">
            {{ item.date }}
          </span>
          <span class="talent__item-addr font-color--basic overflow--ellipsis">
            {{ item.address }}
          </span>
        </div>

        <el-button
          class="talent__item-button linear-gradient--primary font-weight--bold"
          round
          :disabled="item.btnDisabled"
          @click="() => handleClick(item)"
        >
          {{ item.btnText }}
        </el-button>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
defineProps({
  data: {
    type: Array as Components.HomeTalentPropData,
    default: () => [],
  },
})

const handleClick = (data: Home.OverseasTalentAttractionActivity) => {
  if (data.btnDisabled) return
  toLink(data.signUpUrl)
}
</script>

<style scoped lang="scss">
.talent {
  &__item {
    margin-right: 20px;
    margin-bottom: 20px;
    padding: 16px 16px 18px;
    width: 386px;

    &:nth-of-type(3n) {
      margin-right: 0;
    }

    &-image {
      width: 354px;
      height: 187px;
      background: #cfcfcf;
      border-radius: 8px 8px 0px 0px;
    }

    &-logo {
      top: 158px;
      width: 80px;
      height: 80px;
    }

    &-title {
      margin-top: 54px;
      margin-bottom: 20px;
      @include ellipsis-lines(2, 24px, 16px);
    }

    &-box {
      max-width: 230px;
    }

    &-date,
    &-addr {
      padding-left: 20px;
      line-height: 21px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/clock-default.png)
        no-repeat left center / 12px;
    }

    &-addr {
      margin-top: 4px;
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/activity-default.png);
    }

    &-button {
      width: 96px;
      color: var(--color-white);

      &:disabled {
        color: var(--color-white);
        background-color: var(--button-disabled-primary);
      }
    }
  }
}
</style>
