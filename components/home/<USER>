<template>
  <div class="company__container">
    <a
      :href="currentData.url"
      class="company__link flex--row border-radius box-shadow overflow--hidden"
      target="_blank"
      :title="currentData.companyName"
      :rel="currentData.rel"
      @click="() => addShowcase(currentData.id, currentData.number)"
    >
      <div class="company__desc">
        <div class="company__header flex--row">
          <img
            :src="currentData.companyLogo"
            :alt="currentData.companyName"
            class="company__logo image--logo image--contain"
            :data-showcase-number="currentData.number"
            :data-showcase-id="currentData.id"
          />

          <div class="company__title flex--column">
            <span
              class="company__name color--white font-size--22 font-weight--bold overflow--ellipsis"
            >
              {{ currentData.companyName }}
            </span>
            <span class="company__info overflow--ellipsis">
              {{ currentData.companyInfo }}
            </span>
          </div>
        </div>

        <div class="company__content color--white">
          {{ currentData.description }}
        </div>

        <el-button class="company__button" round>查看详情</el-button>
      </div>

      <img
        :src="currentData.image"
        :alt="currentData.title"
        class="company__image image--cover"
      />
    </a>

    <div
      class="company__carousel flex--row flex-justify--between flex-align--center"
    >
      <div
        class="company__carousel-previous company__carousel-button cursor--pointer"
        @click="() => handleCarousel(false)"
      ></div>

      <div
        class="company__carousel-content box-shadow border-radius background-color--default overflow--hidden"
      >
        <el-carousel
          height="138px"
          arrow="never"
          :autoplay="false"
          indicator-position="none"
          ref="carouselRef"
        >
          <el-carousel-item v-for="(items, index) in groupData" :key="index">
            <ul class="company__carousel-list flex--row flex-align--center">
              <li
                v-for="(item, index) in items"
                class="company__carousel-image position--relative cursor--pointer overflow--hidden"
                :class="
                  currentData.id === item.id
                    ? 'company__carousel-image--active'
                    : ''
                "
                :key="item.id"
                @click="() => handleClick(item.id)"
              >
                <img class="image--cover" :src="item.image" :alt="item.title" />

                <span
                  class="company__carousel-box position--absolute flex--column flex--center font-size--14"
                >
                  <img
                    class="image--logo image--contain"
                    :src="item.companyLogo"
                    :alt="item.companyName"
                  />

                  <span class="overflow--ellipsis">
                    {{ item.companyName }}
                  </span>
                </span>
              </li>
            </ul>
          </el-carousel-item>
        </el-carousel>
      </div>

      <div
        class="company__carousel-next company__carousel-button cursor--pointer"
        @click="() => handleCarousel(true)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.HomeCompanyPropData,
    default: () => [],
  },
})

const current = ref(0)

const currentData = computed(() => props.data[current.value])

const groupData = computed(() => {
  const groupSize = 5
  const groups = []

  for (let i = 0; i < props.data.length; i += groupSize) {
    groups.push(props.data.slice(i, i + groupSize))
  }

  return groups
})

const carouselRef = ref()

const handleCarousel = (value: boolean) => {
  const name = value ? 'next' : 'prev'

  unref(carouselRef)[name]()
}

const handleClick = (id: string) => {
  const index = props.data.findIndex((item) => item.id === id)

  current.value = index
}
</script>

<style scoped lang="scss">
.company {
  &__link {
    height: 351px;
  }

  &__header {
    margin-bottom: 28px;
  }

  &__desc {
    padding: 36px 46px 32px 40px;
    width: 646px;
    background: #1b47ac
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
      no-repeat right 162px / 316px 157px;
  }

  &__logo {
    margin-right: 16px;
    width: 64px;
    height: 64px;
  }

  &__title {
    max-width: 465px;
  }

  &__info {
    margin-top: 10px;
    color: rgba($color: $color-white, $alpha: 0.6);
  }

  &__content {
    @include ellipsis-lines(4, 2);
  }

  &__button {
    margin-top: 20px;
    color: var(--color-primary);
  }

  &__image {
    width: 554px;
  }

  &__carousel {
    margin-top: -32px;

    $types: 'previous', 'next';

    @each $type in $types {
      &-#{$type} {
        background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-#{$type}-ghost.png);
      }
    }

    &-button {
      width: 28px;
      height: 28px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }

    &-content {
      width: 1128px;
    }

    &-list {
      padding: 0 4px;
    }

    $alias: &;

    &-image {
      margin: 4px 0;
      width: 224px;
      height: 130px;

      &:nth-of-type(1) {
        border-radius: 12px 0 0 12px;
      }

      &:nth-of-type(5) {
        border-radius: 0 12px 12px 0;
      }

      &--active {
        #{$alias}-box {
          display: none;
        }
      }
    }

    &-box {
      top: 0;
      left: 0;
      padding: 0 14px;
      width: 100%;
      height: 100%;
      color: #0b3e61;
      background-color: rgba($color: $color-white, $alpha: 0.75);

      .image--logo {
        margin-bottom: 16px;
        width: 50px;
        height: 50px;
      }

      .overflow--ellipsis {
        max-width: 196px;
      }
    }
  }
}
</style>
