<template>
  <div
    class="service__container flex--row flex-justify--between flex-align--center"
  >
    <div class="service__content flex--none">
      <div class="service__header flex--row flex-align--center">
        <div class="service__logo">
          <img class="image--contain" src="public/logo-icon.png" alt="" />
        </div>

        <div class="service__title color--white font-size--30">
          <div class="font-weight--bold">
            <span class="name">{{ title }}</span
            >&nbsp;HiTalentGlobal
          </div>

          <p class="font-size--16">
            高才科技旗下平台，专注于海（境）外引才服务
          </p>
        </div>
      </div>

      <ul class="service__list flex--row flex--wrap color--white font-size--15">
        <li>
          <span class="font-size--20 font-weight--bold">超百万</span
          >&nbsp;全球高层次人才资源
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">130+</span
          >&nbsp;海外引才合作站点
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">100+</span
          >&nbsp;海（境）外引才渠道
        </li>
        <li>
          触达&nbsp;<span class="font-size--20 font-weight--bold">20</span
          >&nbsp;个高等教育水平领先国家和地区
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">100+</span
          >&nbsp;海外引才合作单位
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">100+</span
          >&nbsp;场海（境）外落地招聘宣讲活动
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">16</span
          >&nbsp;个落地国家和地区
        </li>
        <li>
          <span class="font-size--20 font-weight--bold">5800+</span
          >&nbsp;名海（境）外到会博士
        </li>
      </ul>

      <div class="service__footer">
        <base-link
          class="more background-color--default"
          size="large"
          width="140px"
          text="了解更多"
          :href="routeList.about"
          target="_self"
        />

        <base-link
          size="large"
          width="140px"
          text="单位合作申请"
          :href="companyCooperationUrl"
          rel="nofollow"
        />
      </div>
    </div>

    <div class="service__image">
      <img
        class="image--contain"
        src="https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>"
        alt=""
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { routeList, title, companyCooperationUrl } = useConfig()
</script>

<style scoped lang="scss">
.service {
  &__container {
    padding: 94px 0 84px;
  }

  &__content {
    padding-top: 24px;
    width: 508px;
  }

  &__logo {
    margin-right: 14px;
    width: 57px;
    height: 57px;
  }

  &__image {
    width: 594px;
    height: 319px;
  }

  &__title {
    .name {
      background: linear-gradient(0deg, #92b1f8 0%, #45aff9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &__list {
    margin-top: 30px;
    height: 145px;

    li {
      width: 54%;

      &:nth-child(odd) {
        width: 46%;
      }
    }
  }

  &__footer {
    margin-top: 30px;

    a {
      color: var(--color-white);
      box-shadow: 0 0 0 1px var(--color-white);

      &.more {
        margin-right: 20px;
        color: var(--color-primary);
      }
    }
  }
}
</style>
