<template>
  <ul class="case__list flex--row">
    <li
      class="border-radius animation-transition"
      v-for="(item, index) in data"
      :key="item.id"
      :class="current === index ? 'is-active' : ''"
      @mouseover="() => handleMouseOver(index)"
    >
      <a
        class="case__item flex--column box-shadow border-radius"
        :href="`${item.url ? item.url : 'javascript:;'}`"
        :title="item.title"
        :rel="item.rel"
        :target="`${item.url ? '_blank' : '_self'}`"
        @click="addShowcase(item.id, item.number)"
      >
        <img
          :src="item.logoImage"
          :alt="item.title"
          class="case__logo border-radius--8"
        />

        <div class="case__title font-size--18 font-weight--bold">
          {{ item.title }}
        </div>

        <div class="case__description flex--none font-color text-align--left">
          {{ item.description }}
        </div>

        <img
          :src="item.otherImage"
          :alt="item.title"
          class="case__image flex--none border-radius"
        />
      </a>
    </li>
  </ul>
</template>

<script setup lang="ts">
defineProps({
  data: {
    type: Array as Components.HomeCasePropData,
    default: () => [],
  },
})

const current = ref(0)

const handleMouseOver = (index: number) => {
  current.value = index
}
</script>

<style scoped lang="scss">
.case {
  $alias: &;

  &__list {
    li {
      margin-right: 20px;

      &.is-active,
      &:hover {
        background: linear-gradient(0deg, #dbe4f9, var(--color-white));
        transform: translateY(-20px);

        #{$alias}__item {
          background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
        }
      }

      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }

  &__item {
    padding: 10px 30px 40px;
    width: 386px;
    height: 550px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
      no-repeat right bottom / 244px 121px;
  }

  &__logo {
    max-height: 40px;
    object-fit: contain;
  }

  &__title {
    margin-top: 10px;
    padding-top: 10px;
    line-height: 27px;
    box-shadow: 0 -1px 0 0 #91acff;
  }

  &__description {
    margin-top: 22px;

    @include ellipsis-lines(6, 2, 14px, true);
  }

  &__image {
    margin-top: 20px;
    width: 326px;
    height: 120px;
    object-fit: cover;
  }
}
</style>
