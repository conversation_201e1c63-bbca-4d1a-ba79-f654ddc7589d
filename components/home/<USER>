<template>
  <div class="recommend__container">
    <el-carousel
      height="360px"
      arrow="never"
      :autoplay="false"
      :interval="5000"
      indicator-position="none"
      ref="carouselRef"
      @change="handleChange"
    >
      <el-carousel-item
        v-for="item in data"
        :key="item.id"
        class="recommend__item position--relative"
      >
        <div class="recommend__image border-radius overflow--hidden">
          <img class="image--cover" :src="item.image" :alt="item.title" />
        </div>

        <div
          class="recommend__card position--absolute position--y-center border-radius box-shadow"
        >
          <!-- 1 -> 归国活动； 2 -> 出海引才； -->
          <base-tag
            class="recommend__tag position--absolute"
            :type="booleanString(item.type) ? 'primary' : 'warning'"
            :text="item.tag"
            radius="right"
          />

          <a
            class="recommend__link"
            :href="item.url"
            target="_blank"
            :title="item.title"
            :rel="item.rel"
          >
            <div class="recommend__link-header flex--row flex-align--center">
              <img
                class="recommend__link-logo flex-none image--logo image--contain"
                :src="item.logo"
                :alt="item.title"
              />

              <div class="recommend__link-title font-weight--bold">
                {{ item.title }}
              </div>
            </div>

            <div class="recommend__link-content font-color">
              {{ item.description }}
            </div>
          </a>

          <div class="recommend__description flex--row flex-align--center">
            <div
              class="recommend__date flex--none color--primary border-radius--8"
            >
              {{ item.date }}
            </div>

            <div
              class="recommend__address font-color--basic overflow--ellipsis"
              :class="
                booleanString(item.type) ? 'recommend__address--primary' : ''
              "
            >
              {{ item.address }}
            </div>
          </div>

          <div class="flex--row flex-justify--between flex-align--center">
            <a
              class="color--primary"
              :href="item.url"
              target="_blank"
              :title="item.title"
              :rel="item.rel"
            >
              了解详情>
            </a>

            <div class="recommend__navigator flex--row flex-align--center">
              <span
                class="recommend__navigator--previous cursor--pointer"
                @click="handlePrevious"
              />

              <div class="recommend__navigator--pager font-size--13">
                <span
                  class="recommend__navigator--current color--primary font-size--20 font-weight--bold"
                >
                  {{ currentText }}&nbsp;</span
                >/&nbsp;{{ data.length }}
              </div>

              <span
                class="recommend__navigator--next cursor--pointer"
                @click="handleNext"
              />
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <ul class="recommend__pagination flex--row">
      <li
        v-for="(item, index) in data"
        :key="item.id"
        class="recommend__pagination-item position--relative flex--row flex--center border-radius cursor--pointer"
        :style="{ backgroundImage: `url(${item.image})` }"
        :class="index === current ? 'recommend__pagination-item--active' : ''"
        @click="() => handleCarousel(index)"
      >
        <span class="position--absolute position--center text-align--center">
          {{ item.otherDescriptionOne }}
        </span>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.HomeRecommendPropData,
    default: () => [],
  },
})

const carouselRef = ref()

const current = ref(0)

const totalMaxIndex = computed(() => props.data.length - 1)

const currentText = computed(() => current.value + 1)

const handleCarousel = (value: number) => {
  const val =
    value < 0 ? totalMaxIndex.value : value > totalMaxIndex.value ? 0 : value

  current.value = val
  carouselRef.value.setActiveItem(val)
}

const handleChange = (value: number) => {
  current.value = value
}

const handlePrevious = () => {
  handleCarousel(current.value - 1)
}

const handleNext = () => {
  handleCarousel(current.value + 1)
}
</script>

<style scoped lang="scss">
.recommend {
  &__image {
    width: 680px;
    height: 360px;
  }

  &__card {
    right: 10px;
    padding: 28px 30px;
    width: 575px;
    height: 306px;
    background: var(--color-white)
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
      no-repeat left bottom / 501px 102px;
  }

  &__tag {
    top: 0;
    right: 0;
  }

  &__link {
    &-header {
      padding-bottom: 20px;
      border-bottom: 1px solid var(--color-border);
    }

    &-logo {
      margin-right: 10px;
      width: 52px;
      height: 52px;
    }

    &-title {
      @include ellipsis-lines(2, 27px, 18px, true);
    }

    &-content {
      margin: 10px auto;

      @include ellipsis-lines(3, 26px);
    }
  }

  &__description {
    margin-bottom: 10px;
  }

  &__date {
    margin-right: 10px;
    padding: 4px 14px;
    background-color: var(--label-background-primary);
  }

  &__address {
    padding-left: 16px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/activity-default.png)
      no-repeat left center / 12px;

    &--primary {
      background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png);
    }
  }

  &__navigator {
    $list: 'previous', 'next';

    @each $item in $list {
      &--#{$item} {
        width: 34px;
        height: 34px;
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/button-#{$item}.png)
          no-repeat
          center
          center /
          100%;
      }
    }

    &--pager {
      padding: 0 38px;
    }
  }

  &__pagination {
    margin-top: 30px;

    &-item {
      margin-right: 10px;
      width: 232px;
      height: 89px;
      color: #0b3e61;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;

      &:last-of-type {
        margin-right: 0;
      }

      &::before {
        content: '';
        width: 100%;
        height: 58px;
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/home/<USER>
          no-repeat center / cover;
      }

      span {
        padding: 0 40px;
        width: 100%;

        @include ellipsis-lines(2, 21px, 13px, true);
      }

      &--active {
        &::before,
        span {
          display: none;
        }
      }
    }
  }
}
</style>
