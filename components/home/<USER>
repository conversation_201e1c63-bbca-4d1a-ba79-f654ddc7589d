<template>
  <div class="activity__container">
    <ul v-if="hasData(topList)" class="activity__top flex--row">
      <li
        v-for="(item, index) in topList"
        class="activity__top-item position--relative border-radius overflow--hidden animation-mouseover"
        :key="index"
      >
        <img
          class="position--absolute image--cover"
          :src="item.image"
          :alt="item.title"
        />

        <base-tag
          class="position--absolute"
          style="top: 0; right: 0; z-index: 1"
          radius="right"
          :text="item.tag"
        />

        <div class="activity__top-content position--absolute position--center">
          <a
            class="flex--column"
            :href="item.url"
            target="_blank"
            :title="item.title"
            :rel="item.rel"
          >
            <span class="activity__top-address overflow--ellipsis">
              {{ item.address }}
            </span>

            <span class="activity__top-date color--white">{{ item.date }}</span>

            <span class="activity__top-title color--white font-weight--bold">
              {{ item.title }}
            </span>
          </a>

          <el-button
            class="activity__button font-weight--bold background-color--default"
            round
            :disabled="item.btnDisabled"
            @click="() => handleClick(item)"
          >
            {{ item.btnText }}
          </el-button>
        </div>
      </li>
    </ul>

    <ul v-if="hasData(bottomList)" class="activity__bottom flex--row">
      <li
        v-for="(item, index) in bottomList"
        class="activity__bottom-item flex--column position--relative border-radius box-shadow overflow--hidden animation-mouseover--border"
        :key="index"
      >
        <base-tag
          class="position--absolute"
          style="top: 0; left: 0"
          :text="item.tag"
        />

        <a :href="item.url" target="_blank" :title="item.title" :rel="item.rel">
          <div class="activity__bottom-image">
            <img class="image--cover" :src="item.image" :alt="item.title" />
          </div>

          <div
            class="activity__bottom-date activity__bottom-padding font-color--label overflow--ellipsis"
          >
            {{ item.date }}
          </div>

          <div
            class="activity__bottom-title activity__bottom-padding font-weight--bold"
          >
            {{ item.title }}
          </div>
        </a>

        <div
          class="activity__bottom-footer activity__bottom-padding flex--row flex-justify--between flex-align--center"
        >
          <span class="font-color--basic overflow--ellipsis">
            {{ item.address }}
          </span>

          <el-button
            class="activity__button flex--none font-weight--bold linear-gradient--primary"
            round
            :disabled="item.btnDisabled"
            @click="() => handleClick(item)"
          >
            {{ item.btnText }}
          </el-button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.HomeActivityPropData,
    default: () => {},
  },
})

const topList = computed(() => props.data.topList)

const bottomList = computed(() => props.data.bottomList)

const handleClick = (data: Home.TopList | Home.BottomList) => {
  if (data.btnDisabled) return
  toLink(data.signUpUrl)
}
</script>

<style scoped lang="scss">
.activity {
  $alias: &;

  &__button {
    width: 96px;
    color: var(--color-primary);

    &:disabled {
      color: var(--color-white);
      background-color: var(--button-disabled-primary);
      border-color: var(--button-disabled-primary);
    }
  }

  &__top {
    &-item {
      margin-right: 20px;
      margin-bottom: 20px;

      width: 590px;
      height: 272px;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          #234283 0%,
          rgba(30, 63, 132, 0.38) 77%,
          rgba(164, 192, 250, 0.2) 100%
        );
      }

      &:last-of-type {
        margin-right: 0;
      }
    }

    &-content {
      padding: 0 30px;
      width: 100%;
      z-index: 1;
    }

    &-address {
      margin-bottom: 6px;
      padding-left: 20px;
      color: rgba($color: $color-white, $alpha: 0.8);
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-white.png)
        no-repeat left center / 12px;
    }

    &-title {
      margin-top: 12px;
      @include ellipsis-lines(2, 40px, 24px);
    }

    #{$alias}__button {
      margin-top: 20px;
    }
  }

  &__bottom {
    &-item {
      margin-right: 20px;

      width: 285px;
      height: 302px;

      &:last-of-type {
        margin-right: 0;
      }
    }

    &-padding {
      padding: 0 15px;
    }

    &-image {
      margin-bottom: 12px;
      height: 150px;
    }

    &-title {
      margin-top: 10px;
      @include ellipsis-lines(2, 24px, 16px);
    }

    &-footer {
      margin-top: 10px;
    }

    #{$alias}__button {
      margin-left: 20px;
      color: var(--color-white);
    }
  }
}
</style>
