<template>
  <span :class="tagClassName">{{ text }}</span>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String as Components.BaseTagPropType,
    default: 'primary',
  },

  size: {
    type: String as Components.BaseTagPropSize,
    default: '',
  },

  radius: {
    type: String as Components.BaseTagPropRadius,
    default: 'left',
  },

  text: {
    type: String,
    default: '',
  },
})

const modifierClassName = computed(
  () =>
    `${props.type ? `tag--${props.type}` : ''} tag--${props.radius} ${props.size && props.size !== 'default' ? `tag--${props.size}` : ''}`,
)

const tagClassName = computed(
  () =>
    `tag__container display--inline-block font-size--12 ${modifierClassName.value}`,
)
</script>

<style scoped lang="scss">
.tag {
  $types: (
    primary: (
      var(--color-primary),
      var(--label-background-primary),
    ),
    warning: (
      #ffa000,
      #fff3e0,
    ),
    default: (
      var(--color-primary),
      var(--color-white),
    ),
  );

  $sizes: (
    'large': 28,

    'default': 24,

    'small': 20,
  );

  $type-default: map-get(
    $map: $types,
    $key: 'primary',
  );

  $size-default: map-get(
    $map: $sizes,
    $key: 'default',
  );

  &__container {
    padding: 0 10px;
    color: nth($list: $type-default, $n: 1);
    line-height: $size-default + px;
    background-color: nth($list: $type-default, $n: 2);
    border-radius: 12px 0px 8px 0px;
  }

  &--right {
    border-radius: 0px 12px 0px 8px;
  }

  @each $type in map-keys($map: $types) {
    &--#{$type} {
      color: nth($list: map-get($types, $type), $n: 1);
      background-color: nth($list: map-get($types, $type), $n: 2);
    }
  }

  @each $size in map-keys($map: $sizes) {
    &--#{$size} {
      $size-value: map-get($sizes, $size);
      line-height: $size-value + px;
    }
  }
}
</style>
