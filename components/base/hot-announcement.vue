<template>
  <a
    class="hot-announcement__container flex--row border-radius"
    :href="data.url"
    target="_blank"
    :title="data.title"
  >
    <div class="hot-announcement__serial flex--row flex--center">
      <base-sort :index="data.index" />
    </div>

    <div class="hot-announcement__info flex--1 overflow--hidden">
      <div
        class="hot-announcement__title font-size--18 font-weight--bold overflow--ellipsis"
      >
        {{ data.title }}
      </div>

      <div
        v-if="hasData(data.highlightsDescribe)"
        class="hot-announcement__lightspot font-color--basic overflow--ellipsis"
      >
        {{ data.highlightsDescribe }}
      </div>

      <div
        v-else-if="hasData(data.major)"
        class="hot-announcement__tag display--inline-block font-color border-radius--4 background-color--primary overflow--ellipsis"
      >
        {{ data.major }}
      </div>

      <div
        v-else
        class="hot-announcement-company__name font-color--basic overflow--ellipsis"
      >
        {{ data.companyName }}
      </div>

      <div
        class="hot-announcement__bottom flex--row flex-justify--between font-color--basic"
      >
        <div
          class="hot-announcement__welfare flex--row flex-align--center flex--1"
        >
          <div class="hot-announcement__region">{{ data.address }}&nbsp;</div>
          <div class="hot-announcement__job">
            |
            <span class="color--primary">{{ data.jobAmount }}&nbsp;</span
            >职位&nbsp;
          </div>
          <div class="hot-announcement__person">
            | 招<span class="color--primary">{{ data.recruitAmount }}</span
            >人
          </div>
        </div>
        <div class="hot-announcement__date">{{ data.refreshDate }}发布</div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseHotAnnouncementProp,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.hot-announcement {
  &__container {
    padding: 16px 20px 15px 0;
    background-color: var(--color-white);

    &:hover {
      box-shadow: $box-shadow-primary;
    }
  }

  &__serial {
    width: 68px;
    flex-shrink: 0;
  }

  $serial: first, second, third;

  @each $key in $serial {
    &__serial--#{$key} {
      width: 28px;
      height: 36px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/#{$key}.png)
        no-repeat
        left
        top/contain;
    }
  }

  &__lightspot {
    margin-top: 10px;
    margin-bottom: 13px;
    padding-left: 30px;
    padding-right: 10px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/praise.png)
      no-repeat left/26px 22px;
  }

  &__tag {
    margin-top: 9px;
    margin-bottom: 10px;
    padding: 0 10px;
    line-height: 24px;
    max-width: 340px;
  }

  &-company__name {
    margin-top: 10px;
    margin-bottom: 13px;
  }

  &__region {
    padding-left: 17px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png)
      no-repeat left / 12px 12px;
  }
}
</style>
