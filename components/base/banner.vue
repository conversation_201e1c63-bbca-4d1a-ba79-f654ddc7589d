<template>
  <div v-if="show" class="banner__container">
    <el-carousel
      class="carousel--primary carousel__button--large"
      trigger="click"
      height="380px"
      arrow="never"
      :interval="5000"
      :indicator-position="data.length > 1 ? '' : 'none'"
    >
      <el-carousel-item v-for="item in data" :key="item.image">
        <a
          class="flex--row flex--center"
          :href="item.url"
          target="_blank"
          :rel="item.rel"
          @click="addShowcase(item.id, item.number)"
        >
          <img :src="item.image" alt="" />
        </a>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.BaseBannerPropData,
    default: () => [],
  },
})

const show = computed(() => props.data.length > 0)
</script>

<style scoped lang="scss">
.banner {
  &__container {
    a {
      height: 100%;
    }

    .image--contain {
      width: auto;
    }
  }
}
</style>
