<template>
  <div class="title__container" :class="titleContainerClassName">
    <div
      class="title__text flex--row position--relative"
      :class="titleTextClassName"
    >
      <span
        v-if="title"
        class="title__name font-size--24 font-weight--bold"
        :class="titleNameClassName"
      >
        {{ title }}
      </span>

      <template v-else>
        <slot name="prefix-icon"></slot>

        <span
          class="title__prefix font-size--36 font-weight--bold"
          :class="titleComputedData.prefix.className"
        >
          {{ titleComputedData.prefix.text }}
        </span>
        <span
          class="title__suffix font-size--36 font-weight--bold"
          :class="titleComputedData.suffix.className"
        >
          {{ titleComputedData.suffix.text }}
        </span>
      </template>

      <span
        class="title__english position--absolute font-weight--bold"
        :class="titleEnglishClassName"
      >
        {{ english }}
      </span>
    </div>

    <div
      v-if="description"
      class="title__description position--relative font-color--basic text-align--center"
      :class="titleDescriptionClassName"
    >
      {{ description }}
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },

  /**
   * 只作用于 `title` 样式处理
   * @default false
   */
  primary: {
    type: Boolean,
    default: false,
  },

  /**
   * 只是反转 渐变 的样式，默认 `prefix` 渐变
   * @default false
   */
  reverse: {
    type: Boolean,
    default: false,
  },

  /**
   * 小尾巴，只有 `reverse` 和 `tail` 为真时才有效
   * @default false
   */
  tail: {
    type: Boolean,
    default: false,
  },

  prefix: {
    type: String,
    default: '',
  },

  suffix: {
    type: String,
    default: '',
  },

  english: {
    type: String,
    default: '',
  },

  description: {
    type: String,
    default: '',
  },
})

const titleContainerClassName = computed(() =>
  props.title
    ? `title__container--${props.description ? 'none' : 'default'}`
    : '',
)

const titleTextClassName = computed(
  () => `${props.title ? '' : 'flex--center'}`,
)

const titleNameClassName = computed(
  () => `${props.primary ? 'color--primary font-size--32' : ''}`,
)

const titleEnglishClassName = computed(
  () =>
    `${props.title ? `${props.primary ? 'title__english--primary font-size--18' : 'title__english--default font-size--24'}` : 'position--x-center font-size--32'}`,
)

const titleDescriptionClassName = computed(
  () => `${props.title && props.primary ? 'font-size--14' : 'font-size--16'}`,
)

const titleComputedData = computed(() => {
  const { reverse, tail, prefix, suffix } = props
  const className = `linear-gradient--primary title--fill ${reverse && tail ? 'position--relative title--tail' : ''}`

  const key = reverse ? 'suffix' : 'prefix'

  const titleData = {
    prefix: { className: '', text: prefix },
    suffix: { className: '', text: suffix },
  }

  titleData[key].className = className

  return titleData
})
</script>

<style scoped lang="scss">
.title {
  &__container {
    padding: 60px 0;
    white-space: nowrap;

    &--default {
      padding: 0 0 10px;
    }

    &--none {
      padding: 0;
    }
  }

  &__text {
    z-index: 1;
  }

  &--fill {
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  &--tail {
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      width: 112px;
      height: 62px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/base/title.png)
        no-repeat center / contain;
      transform: translateX(-60%);
    }
  }

  &__english {
    top: 22px;
    color: #e5edff;
    z-index: -1;

    &--default {
      top: 11px;
    }

    &--primary {
      top: 28px;
    }
  }

  &__description {
    margin-top: 8px;
    z-index: 2;
  }
}
</style>
