<template>
  <div
    class="global-activity__container position--relative"
    :class="`global-activity__container--${type}`"
  >
    <div class="global-activity__detail border-radius">
      <a
        :href="data.url"
        target="_blank"
        :title="data.name"
        :rel="data.isOutsideUrl == 1 ? 'nofollow' : ''"
      >
        <div class="global-activity__header flex--row flex-align--center">
          <div class="global-activity__logo">
            <img
              class="image--contain image--logo"
              :src="data.logo"
              :alt="data.name"
            />
          </div>
          <div
            class="global-activity__title flex--1 font-size--20 font-weight--bold"
          >
            {{ data.name }}

            <base-tag
              class="global-activity-tag"
              :type="type === 'abroad' ? 'warning' : 'primary'"
              :text="data.tag"
              size="small"
            />
          </div>
        </div>

        <div class="global-activity__intro font-color overflow--hidden">
          {{ data.description }}
          &nbsp;<span class="global-activity__view font-color--basic"
            >阅读全文</span
          >
        </div>
      </a>

      <div class="flex--row">
        <el-popover
          placement="right-start"
          :width="361"
          trigger="hover"
          :teleported="false"
        >
          <template #reference>
            <div>
              <div
                class="global-activity__date font-color--basic overflow--ellipsis"
              >
                活动日期：{{ data.date }}
              </div>
              <div
                class="global-activity__address font-color--basic overflow--ellipsis"
              >
                {{ type === 'native' ? '活动地点：' : '活动场次：'
                }}{{ data.address }}
              </div>
            </div>
          </template>

          <div class="global-activity__content">
            <div
              class="global-activity__session"
              v-for="item in data.sessionList"
            >
              <div
                class="global-activity__session-title font-color font-weight--bold"
              >
                {{ item.name }}
              </div>
              <div class="global-activity__session-date font-color--basic">
                时间：{{ item.activityDate }}
              </div>
              <div
                class="global-activity__session-address flex--row font-color--basic"
              >
                <div class="global-activity__session-label">地点：</div>
                <div class="global-activity__session-value">
                  {{ item.activityAddress }}
                </div>
              </div>
            </div>
          </div>
        </el-popover>
      </div>

      <div class="global-activity__bottom flex--row flex-align--center">
        <el-button
          size="large"
          class="global-activity-button font-weight--bold"
          round
          :disabled="data.btnDisabled"
          @click="handleOpen(data.signUpUrl)"
        >
          {{ data.btnText }}
        </el-button>

        <div
          v-if="hasData(data.signEndDate)"
          class="global-activity__cut-foo font-color--basic flex--1 overflow--ellipsis"
        >
          报名截止日期：<span class="color--primary">{{
            data.signEndDate
          }}</span>
        </div>
      </div>
    </div>

    <div
      class="global-activity__banner position--absolute border-radius overflow--hidden background-color--default"
    >
      <el-carousel
        class="border-radius"
        height="258px"
        :autoplay="false"
        :arrow="data.imageInfo.list.length > 1 ? 'always' : 'never'"
        indicator-position="none"
      >
        <el-carousel-item v-for="item in data.imageInfo.list">
          <div class="global-activity__picture position--relative">
            <div class="global-activity__picture--tag position--absolute">
              {{ data.imageInfo.tag }}
            </div>
            <img class="image--contain" :src="item" :alt="data.name" />
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String as Components.BaseGlobalActivityType,
    default: 'native',
  },
  data: {
    type: Object as Components.BaseGlobalActivityProp,
    default: {},
  },
})

const handleOpen = (url: string) => {
  toLink(url)
}
</script>

<style scoped lang="scss">
.global-activity {
  &__container {
    $color-white: #fff;
    $background-color-activity: #4778e8;

    &::before {
      position: absolute;
      content: '';
      display: block;
      width: 66.66%;
      height: 4px;
      background: linear-gradient(
        90deg,
        $background-color-activity,
        $color-white
      );
      border-radius: 2px 1px 1px 0px;
      top: 0;
      left: 0;
    }

    $prefix: '.global-activity';

    #{$prefix}__detail {
      min-height: 302px;
      width: 1080px;
      padding: 18px 400px 20px 20px;
    }

    $type: (
      abroad: (
        'address-icon': activity,
      ),
      native: (
        'address-icon': address,
      ),
    );
    @each $key in map-keys($map: $type) {
      $address-icon: map-get(
        $map:
          map-get(
            $map: $type,
            $key: $key,
          ),
        $key: 'address-icon',
      );

      &--#{$key} {
        #{$prefix}__detail {
          background:
            url(https://img.gaoxiaojob.com/uploads/haiwai/images/activity/#{$key}-activity-bg.png)
              no-repeat
              490px
              bottom/205px
              auto,
            $color-white;
        }

        #{$prefix}__address {
          background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/#{$address-icon}-default.png);
        }

        &:hover {
          #{$prefix}__detail {
            background:
              url(https://img.gaoxiaojob.com/uploads/haiwai/images/activity/#{$key}-activity-bg.png)
                no-repeat
                490px
                bottom/205px
                auto,
              url(https://img.gaoxiaojob.com/uploads/haiwai/images/activity/global-hover-bg.png)
                no-repeat
                left
                bottom/cover;
          }
        }
      }
    }
  }

  &__logo {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  &__title {
    line-height: 30px;
  }

  &-tag {
    transform: translateY(-4px);
  }

  &__header {
    height: 56px;
    margin-bottom: 12px;
  }

  &__intro {
    height: 78px;
    margin-bottom: 12px;
    line-height: 26px;
  }

  &-tag--hover {
    display: none;
  }

  &__view {
    text-decoration: underline;
  }

  &__date {
    max-width: 478px;
    padding-left: 20px;
    margin-bottom: 7px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/clock-default.png)
      no-repeat left/12px 12px;
  }

  &__address {
    max-width: 478px;
    padding-left: 20px;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 12px 12px;
  }

  &__bottom {
    margin-top: 17px;
  }

  &__bottom {
    margin-top: 17px;
  }

  &-button {
    color: $color-white;
    padding-left: 34px;
    padding-right: 34px;
    background: linear-gradient(
      90deg,
      var(--color-primary),
      var(--color-second)
    );
    border: none;

    & + .global-activity-button {
      margin-left: 20px;
    }

    &:disabled {
      color: $color-white;
      background: var(--button-disabled-primary);
    }
  }

  &__cut-foo {
    margin-left: 10px;
  }

  &__banner {
    padding: 2px;
    right: 0;
    top: 20px;
    max-height: 262px;
    width: 492px;
    bottom: 20px;

    :deep() {
      .el-carousel__arrow {
        width: 24px;
        height: 24px;

        .el-icon {
          display: none;
        }
      }

      .el-carousel__arrow--left {
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/carousel-arrow-left.png)
          no-repeat left/24px 24px;
      }

      .el-carousel__arrow--right {
        background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/carousel-arrow-right.png)
          no-repeat left/24px 24px;
      }
    }
  }

  &__picture {
    width: 488px;
    height: 258px;

    &--tag {
      color: $color-white;
      right: 0;
      top: 0;
      line-height: 24px;
      padding: 0 10px;
      border-radius: 0px 12px 0px 8px;
      background-color: rgba($color: $font-color-default, $alpha: 0.5);
    }
  }

  &__content {
    padding: 0 12px 0 10px;
    max-height: 280px;
    overflow: auto;
  }

  &__session {
    margin-bottom: 12px;
  }

  &__session-date {
    margin-top: 3px;
    margin-bottom: 7px;
  }

  &__session-label {
    flex-shrink: 0;
  }

  &__session-title {
    line-height: 21px;
  }
}

:deep() {
  .el-popover {
    border-radius: 12px !important;
    transform: translateX(-10px) translateY(4px);
    padding-right: 10px;
  }
}
</style>
