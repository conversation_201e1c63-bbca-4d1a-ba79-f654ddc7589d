<template>
  <a
    class="more-activity__container flex--row border-radius animation-mouseover--border"
    :href="data.url"
    target="_blank"
    :title="data.title"
    :rel="data.isOutsideUrl == 1 ? 'nofollow' : ''"
  >
    <div class="more-activity__aside">
      <div
        class="more-activity__date font-size--24 color--primary font-weight--bold"
      >
        {{ data.monthDay }}
      </div>
      <div class="more-activity__years color--primary">
        {{ data.year }}
      </div>
    </div>

    <div class="more-activity__info flex--1 overflow--hidden">
      <div class="more-activity__title font-weight--bold">
        {{ data.title }}
      </div>

      <div
        v-if="data.highlightsDescribe"
        class="flex--row flex-justify--between"
      >
        <div class="more-activity__highlights flex--1 overflow--ellipsis">
          {{ data.highlightsDescribe }}
        </div>
        <div
          class="more-activity__address overflow--ellipsis font-color--basic"
        >
          {{ data.address }}
        </div>
      </div>

      <div v-else class="flex--row flex-justify--between font-color--basic">
        <div class="more-activity__data overflow--ellipsis">
          {{ type === 'native' ? '截止日期：' : '活动日期：' }}{{ data.day }}
        </div>
        <div class="more-activity__address overflow--ellipsis">
          {{ data.address }}
        </div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String as Components.BaseGlobalActivityType,
    default: 'native',
  },
  data: {
    type: Object as Components.BaseMoreActivityProp,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.more-activity {
  &__container {
    background-color: var(--color-white);
    border: 1px solid #d4e1ff;
    min-height: 118px;
    width: calc((100% - 20px) / 2);
  }

  &__aside {
    width: 100px;
    margin: 4px 0 4px 4px;
    padding: 30px 5px 0 15px;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    flex-shrink: 0;
    background:
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/time.png)
        no-repeat right bottom/47px 48px,
      #e4ebfc;
  }

  &__years {
    opacity: 0.8;
  }

  &__info {
    padding: 12px 20px 16px;
  }

  &__title {
    margin-bottom: 12px;
    @include ellipsis-lines(2, 28px, 16px);
  }

  &__highlights {
    color: var(--color-highlight);
    max-width: 326px;
    padding-left: 33px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/praise.png)
      no-repeat left/26px 22px;

    & + .more-activity__address {
      max-width: 100px;
    }
  }

  &__address {
    max-width: 198px;
    padding-left: 18px;
    margin-left: 18px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/activity-default.png)
      no-repeat left/12px 12px;
  }
}
</style>
