<template>
  <div class="advertise__container">
    <div class="advertise__list">
      <div class="flex--row advertise__top">
        <a
          v-for="item in data.topShowcaseList"
          class="advertise__item animation-mouseover border-radius background-color--default cursor--pointer"
          :href="item.url"
          target="_blank"
          :rel="item.rel"
          @click="addShowcase(item.id, item.number)"
        >
          <img :src="item.image" class="border-radius" :alt="item.title" />
        </a>
      </div>

      <div class="flex--row flex--wrap">
        <a
          v-for="item in data.bottomShowcaseList"
          class="advertise__item animation-mouseover border-radius advertise__small cursor--pointer background-color--default"
          :href="item.url"
          :rel="item.rel"
          target="_blank"
          @click="addShowcase(item.id, item.number)"
        >
          <img class="border-radius" :src="item.image" :alt="item.title" />
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  data: {
    type: Object as Components.BaseAdvertise,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.advertise {
  &__container {
    padding: 15px 0;
  }

  &__item {
    display: block;
    flex: none;
    width: 590px;
    height: 90px;
    margin-right: 20px;

    &:last-of-type {
      margin-right: 0;
    }
  }

  &__top {
    margin-bottom: 7px;

    img {
      width: 590px;
      height: 90px;
    }
  }

  &__small {
    width: 290px;
    margin-right: 13px;
    margin-top: 13px;

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 90px;
    }
  }
}
</style>
