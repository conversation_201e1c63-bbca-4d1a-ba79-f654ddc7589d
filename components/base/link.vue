<template>
  <a
    class="link__container display--inline-block color--primary font-weight--bold text-align--center border-radius--16"
    :class="calcClassName"
    :style="calcStyle"
    :href="href"
    :target="target"
    :rel="rel"
  >
    {{ text }}
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  href: {
    type: String,
  },

  target: {
    type: String as Components.BaseLinkPropTarget,
    default: '_blank',
  },

  rel: {
    type: String as Components.BaseLinkPropRel,
    default: '',
  },

  text: {
    type: String,
    default: '查看更多',
  },

  width: {
    type: String,
  },

  size: {
    type: String as Components.BaseLinkPropSize,
    default: 'default',
  },
})

const calcClassName = computed(() =>
  props.size && props.size !== 'default' ? `link--${props.size}` : '',
)

const calcStyle = computed(() =>
  props.width ? { padding: 0, width: props.width } : {},
)
</script>

<style scoped lang="scss">
.link {
  $sizes: (
    'large': 40,

    'default': 32,

    'small': 24,
  );

  $size-default: map-get(
    $map: $sizes,
    $key: 'default',
  );

  &__container {
    padding: 0 $size-default + px;
    line-height: $size-default + px;
    box-shadow: $box-shadow-primary;
  }

  @each $size in map-keys($map: $sizes) {
    &--#{$size} {
      $size-value: map-get($sizes, $size);

      padding: 0 $size-value + px;
      line-height: $size-value + px;
      border-radius: calc($size-value / 2) + px;
    }
  }
}
</style>
