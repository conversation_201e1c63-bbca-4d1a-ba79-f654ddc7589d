<template>
  <a
    class="hot-job__container flex--row border-radius"
    :href="data.url"
    target="_blank"
    :title="data.jobName"
  >
    <div class="hot-job__serial flex--row flex--center">
      <base-sort :index="data.index" />
    </div>

    <div class="flex--1 overflow--hidden">
      <div class="hot-job__header flex--row flex-justify--between">
        <div
          class="hot-job__title font-size--18 font-weight--bold flex--1 overflow--ellipsis"
        >
          {{ data.jobName }}
        </div>
        <div class="hot-job__salary font-size--18 font-weight--bold">
          {{ data.wage }}
        </div>
      </div>

      <div class="hot-job__info flex--row font-color">
        <div
          v-if="hasData(data.address)"
          class="hot-job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.address }}
        </div>
        <div
          v-if="hasData(data.major)"
          class="hot-job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.major }}
        </div>
        <div
          v-if="hasData(data.recruitAmount)"
          class="hot-job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.recruitAmount }}
        </div>
      </div>

      <div class="hot-job__bottom font-color--basic">
        <div class="flex--row flex-align--center flex-justify--between">
          <div
            v-if="hasData(data.announcementName)"
            class="flex--1 flex--row flex-align--center overflow--hidden"
          >
            <base-tag
              class="hot-job__tag"
              size="small"
              type="warning"
              text="关联公告"
            />
            <div class="flex--1 overflow--ellipsis">
              {{ data.announcementName }}
            </div>
          </div>
          <div v-else class="hot-job__company flex--1 overflow--ellipsis">
            {{ data.companyName }}
          </div>

          <div class="hot-job__release">{{ data.refreshDate }}发布</div>
        </div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseHotJobProp,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.hot-job {
  &__container {
    padding: 13px 20px 15px 0;
    background-color: var(--color-white);

    &:hover {
      box-shadow: $box-shadow-primary;
    }
  }

  &__serial {
    width: 68px;
    flex-shrink: 0;
  }

  &__salary {
    color: #fa635c;
    margin-left: 18px;
  }

  &__info {
    margin-top: 9px;
  }

  &__info--tag {
    padding: 0 10px;
    line-height: 24px;
    max-width: 170px;
    background-color: var(--tag-background-primary);

    & + .hot-job__info--tag {
      margin-left: 8px;
    }
  }

  &__bottom {
    margin-top: 13px;
  }

  &__tag {
    margin-right: 7px;
  }

  &__release {
    margin-left: 17px;
  }
}
</style>
