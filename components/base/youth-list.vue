<template>
  <ul class="youth__list flex--row flex--wrap" v-if="data.length">
    <li
      v-for="item in data"
      :key="item.id"
      class="youth__item flex--column border-radius box-shadow animation-mouseover--border overflow--hidden"
    >
      <a
        class="youth__link background-color--default"
        :href="item.url"
        target="_blank"
        :title="item.title"
        :rel="item.rel"
      >
        <div class="youth__item-title font-size--16 font-weight--bold">
          {{ item.title }}
        </div>

        <div
          v-if="item.highlightsDescribe"
          class="youth__highlight overflow--ellipsis"
        >
          {{ item.highlightsDescribe }}
        </div>

        <div
          v-else
          class="youth__item-description flex--row flex-justify--between flex-align--center font-color--basic"
        >
          <span class="youth__date youth__icon overflow--ellipsis">
            {{ item.date }}
          </span>
          <span class="youth__addr youth__icon overflow--ellipsis">
            {{ item.address }}
          </span>
        </div>
      </a>

      <a
        class="youth__link youth__link--footer flex--row flex-justify--between flex-align--center linear-gradient--card"
        :href="item.url"
        target="_blank"
        :title="item.title"
        :rel="item.rel"
      >
        <div class="youth__item-name flex--row flex-align--center">
          <img
            class="image--logo image--contain"
            :src="item.companyLogo"
            :alt="item.companyName"
          />

          <span class="youth__name font-color">{{ item.companyName }}</span>
        </div>

        <el-button
          class="youth__button flex--none linear-gradient--primary"
          round
        >
          {{ item.btnText }}
        </el-button>
      </a>
    </li>
  </ul>
</template>

<script setup lang="ts">
defineProps({
  data: {
    type: Array as Components.BaseYouthListPropData,
    default: () => [],
  },
})
</script>

<style scoped lang="scss">
.youth {
  &__item {
    margin: 0 20px 20px 0;
    width: 386px;

    &:nth-child(3n) {
      margin-right: 0;
    }

    &-title {
      @include ellipsis-lines(2, 24px, 16px);
    }

    &-description {
      margin-top: 6px;
    }

    .image--logo {
      margin-right: 8px;
      width: 36px;
      height: 36px;
    }
  }

  &__highlight {
    margin-top: 8px;
    padding-left: 32px;
    color: var(--color-highlight);
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/praise.png)
      no-repeat left center / 26px 22px;
  }

  &__icon {
    padding-left: 16px;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 12px;
  }

  &__date {
    background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/clock-default.png);
  }

  &__addr {
    margin-left: 10px;
    max-width: 60%;
    background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png);
  }

  &__button {
    margin-left: 22px;
    width: 96px;
    color: var(--color-white);
  }

  &__name {
    @include ellipsis-lines(2, 21px, 14px, true);
  }

  &__link {
    padding: 20px;
    height: 106px;

    &--footer {
      height: 64px;
    }
  }
}
</style>
