<template>
  <a
    class="job__container border-radius animation-mouseover--border box-shadow overflow--hidden"
    :href="data.url"
    target="_blank"
    :title="data.jobName"
  >
    <div class="job__top overflow--hidden">
      <div class="job__header flex--row flex-justify--between">
        <div
          class="job__title font-size--16 font-weight--bold flex--1 overflow--ellipsis"
        >
          {{ data.jobName }}
        </div>
        <div class="job__salary font-size--16 font-weight--bold">
          {{ data.wage }}
        </div>
      </div>

      <div class="job__info flex--row font-color">
        <div
          v-if="hasData(data.address)"
          class="job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.address }}
        </div>
        <div
          v-if="hasData(data.major)"
          class="job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.major }}
        </div>
        <div
          v-if="hasData(data.recruitAmount)"
          class="job__info--tag font-size--12 overflow--ellipsis border-radius--4"
        >
          {{ data.recruitAmount }}
        </div>
      </div>
    </div>

    <div
      class="job__bottom flex--row flex-justify--between linear-gradient--card"
    >
      <div class="job__company flex--row flex--1 overflow--hidden font-color">
        <div class="job__company--logo flex--none">
          <img
            class="image--contain image--logo"
            :src="data.companyLogo"
            :alt="data.companyName"
          />
        </div>
        <div class="overflow--ellipsis">
          {{ data.companyName }}
        </div>
      </div>
      <div class="job__release font-color--label">
        {{ data.refreshDate }}发布
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseJobProp,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.job {
  &__container {
    width: calc((100% - 40px) / 3);
    background-color: var(--color-white);
  }

  &__top {
    width: 100%;
    padding: 18px 20px 20px;
  }

  &__header {
    margin-bottom: 12px;
  }

  &__salary {
    color: #fa635c;
    margin-left: 14px;
  }

  &__info--tag {
    background-color: var(--tag-background-primary);
    line-height: 20px;
    padding: 0 6px;
    max-width: 148px;

    & + .job__info--tag {
      margin-left: 8px;
    }
  }

  &__bottom {
    line-height: 28px;
    padding: 10px 20px;
  }

  &__company--logo {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }

  &__release {
    flex-shrink: 0;
    margin-left: 20px;
  }
}
</style>
