<template>
  <div class="empty__container flex-row text-align--center">
    <img
      src="https://img.gaoxiaojob.com/uploads/haiwai/images/icons/empty.png"
      alt=""
    />
    <div class="empty__description font-color--basic">
      暂无相关{{ emptyType }}，请修改筛选条件试试
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  emptyType: {
    type: String,
    default: '活动',
  },
})
</script>

<style lang="scss" scoped>
.empty {
  &__container {
    img {
      width: 268px;
      height: 196px;
    }
  }

  &__description {
    margin-top: 30px;
  }
}
</style>
