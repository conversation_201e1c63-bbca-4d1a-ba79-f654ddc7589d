<template>
  <div
    class="sort__container font-weight--bold color--primary flex--row flex--center"
    :class="`${className} sort--${size}`"
    :data-number="number"
  ></div>
</template>

<script setup lang="ts">
const props = defineProps({
  index: {
    type: [String, Number],
    default: 0,
  },
  size: {
    type: String as Components.BaseTagPropSize,
    default: 'default',
  },
})

const number = computed(() => {
  const { index } = props
  const number = Number(index) + 1
  return number < 10 ? '0' + number : number
})

const className = computed(() => {
  let name = ''
  const { index } = props
  const number = Number(index) + 1
  switch (number) {
    case 1:
      name = 'sort__container--first'
      break
    case 2:
      name = 'sort__container--second'
      break
    case 3:
      name = 'sort__container--third'
      break
    default:
      name = 'sort__container--number'
      break
  }
  return name
})
</script>

<style scoped lang="scss">
.sort {
  &__container {
    $imageSize: (
      'small': (
        20,
        25,
      ),
      'default': (
        28,
        36,
      ),
      'large': (
        36,
        45,
      ),
    );

    $fontSize: (
      'small': 16,
      'default': 20,
      'large': 24,
    );

    &--number {
      $number: attr(data-number);

      &::after {
        content: $number;
        font-size: inherit;
        font-weight: inherit;
      }
    }

    $serial: first, second, third;
    @each $key in $serial {
      &--#{$key} {
        background-image: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/#{$key}.png);
        background-repeat: no-repeat;
        background-position: center;
      }
    }

    @each $size in map-keys($map: $imageSize) {
      &.sort--#{$size} {
        $width: nth(
          $list: map-get($imageSize, $size),
          $n: 1,
        );
        $height: nth(
          $list: map-get($imageSize, $size),
          $n: 2,
        );
        background-size: $width + px $height + px;
        font-size: map-get($fontSize, $size) + px;

        min-width: $width + px;
        min-height: $height + px;
      }
    }
  }
}
</style>
