<template>
  <a
    class="announcement__container flex--row border-radius animation-mouseover--border position--relative"
    :href="data.url"
    target="_blank"
    :title="data.title"
  >
    <div
      v-if="data.tag"
      class="announcement-tag position--absolute font-size--12"
    >
      {{ data.tag }}
    </div>
    <div class="announcement__aside">
      <div
        class="announcement__date font-size--24 color--primary font-weight--bold"
      >
        {{ data.refreshDate }}
      </div>
      <div class="announcement__years color--primary">
        {{ data.refreshYear }}
      </div>
    </div>

    <div class="announcement__info flex--1 overflow--hidden">
      <div class="announcement__title font-weight--bold">
        {{ data.title }}
      </div>

      <div
        v-if="data.highlightsDescribe"
        class="flex--row flex-justify--between"
      >
        <div class="announcement__highlights flex--1 overflow--ellipsis">
          {{ data.highlightsDescribe }}
        </div>
        <div class="announcement__address overflow--ellipsis font-color--basic">
          {{ data.address }}
        </div>
      </div>

      <div v-else class="flex--row flex-justify--between">
        <div class="announcement__job flex--1 overflow--ellipsis font-color">
          <span class="color--primary">{{ data.jobAmount }}</span
          >职位 | 招<span class="color--primary">{{ data.recruitAmount }}</span
          >人
        </div>
        <div class="announcement__address overflow--ellipsis font-color--basic">
          {{ data.address }}
        </div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseAnnouncementProp,
    default: {},
  },
})
</script>

<style scoped lang="scss">
.announcement {
  &__container {
    background-color: var(--color-white);
    border: 1px solid var(--color-border-default);
    min-height: 118px;
    width: 590px;
  }

  &-tag {
    top: -8px;
    right: -9px;
    line-height: 18px;
    padding: 0 8px;
    color: #fa635c;
    background: #fff0ef;
    border-radius: 10px 9px 9px 0px;
  }

  &__aside {
    width: 100px;
    margin: 4px 0 4px 4px;
    padding: 30px 5px 0 15px;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    flex-shrink: 0;
    background:
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/time.png)
        no-repeat right bottom/47px 48px,
      #e4ebfc;
  }

  &__years {
    opacity: 0.8;
  }

  &__info {
    padding: 12px 20px 16px;
  }

  &__title {
    margin-bottom: 12px;
    @include ellipsis-lines(2, 28px, 16px);
  }

  &__highlights {
    color: var(--color-highlight);
    max-width: 326px;
    padding-left: 33px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/praise.png)
      no-repeat left/26px 22px;

    & + .announcement__address {
      max-width: 100px;
    }
  }

  &__address {
    padding-left: 18px;
    margin-left: 18px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png)
      no-repeat left/12px 12px;
  }

  &__job {
    & + .announcement__address {
      max-width: 205px;
    }
  }
}
</style>
