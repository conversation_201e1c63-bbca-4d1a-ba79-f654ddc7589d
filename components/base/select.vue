<template>
  <div class="select__container">
    <el-select
      :class="{ 'has-value': hasData(String(value)) }"
      :filterable="filterable"
      v-model="value"
      :placeholder="placeholder"
      @change="handleChange"
      clearable
    >
      <el-option
        v-for="item in options"
        :key="item.k"
        :label="item.v"
        :value="item.k"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  filterable: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Array as Components.BaseSelectOptionsProp,
    default: 'default',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const value = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emit('update:modelValue', val)
    emit('change', { value: val })
  },
})

const handleChange = () => {}
</script>

<style scoped lang="scss">
.select {
  &__container {
    min-width: 124px;
  }
}

:deep() {
  .el-select {
    $prefix: '.el-select';

    &__wrapper {
      background-color: var(--background-second);
      box-shadow: initial;

      &.is-hovering {
        &:not(.is-focused) {
          box-shadow: initial;
        }
      }
    }

    &.has-value {
      #{$prefix}__wrapper {
        background-color: var(--label-background-primary);
      }

      #{$prefix}__placeholder {
        color: $color-primary;
      }

      #{$prefix}__caret {
        color: $color-primary;
      }
    }
  }
}
</style>
