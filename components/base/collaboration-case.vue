<template>
  <div
    class="case__container border-radius box-shadow position--relative animation-transition"
  >
    <div class="case__company">
      <img
        class="border-radius image--cover case__background"
        :src="data.otherImageUrl"
        :alt="data.title"
      />
      <div class="case__company-info background-color--default">
        <div class="case__title font-weight--bold" :title="data.title">
          {{ data.title }}
        </div>

        <div class="case__subtitle font-color--basic" :title="data.subTitle">
          {{ data.subTitle }}
        </div>

        <div class="flex--row flex-align--center flex-justify--between">
          <div class="case__company-logo">
            <img
              class="image--contain border-radius--8"
              :src="data.image"
              :alt="data.secondTitle"
            />
          </div>

          <div
            class="font-color--basic overflow--ellipsis"
            :title="data.secondTitle"
          >
            {{ data.secondTitle }}
          </div>
        </div>
      </div>
    </div>

    <div class="case__description border-radius position--absolute">
      <div
        class="color--white font-size--18 font-weight--bold case__description-title"
      >
        案例详情
      </div>
      <div class="case__over color--white scrollbar--primary" ref="parent">
        <div class="case__cover" ref="child">
          {{ data.subTitle }}
        </div>
      </div>

      <div
        v-if="showScroll"
        class="case__scroll background-color--default color--primary text-align--center"
      >
        滚动查看更多
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  data: {
    type: Object as Components.BaseCollaboration,
    default: () => {},
  },
})

const showScroll = ref(false)

const parent = ref()
const child = ref()

const handleScrollBar = () => {
  const parentHeight = parent.value.clientHeight
  const childHeight = child.value.scrollHeight

  showScroll.value = childHeight > parentHeight
}

onMounted(() => {
  setTimeout(() => {
    handleScrollBar()
  }, 300)
})
</script>

<style lang="scss" scoped>
.case {
  &__container {
    width: 386px;

    &:hover {
      .case__company {
        opacity: 0;
      }

      .case__description {
        opacity: 1;
        transition: all 0.3s ease;
        transform: translateY(-10px);
      }
    }
  }

  &__background {
    height: 180px;
  }

  &__company {
    height: 409px;

    &-info {
      position: relative;
      height: 250px;
      margin-top: -40px;
      z-index: 1;
      border-radius: 20px 20px 12px 12px;
      padding: 25px 30px 0;
    }

    &-logo {
      flex: none;
      width: 136px;
      height: 50px;
      margin-right: 20px;
    }
  }

  &__title {
    @include ellipsis-lines(2, 27px, 16px);
    margin-bottom: 25px;
  }

  &__subtitle {
    @include ellipsis-lines(3, 21px, 14px);
    margin-bottom: 20px;
  }

  &__description {
    opacity: 0;
    top: -10px;
    height: 450px;
    width: 100%;
    background: var(--color-primary)
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/case-bg.png)
      no-repeat bottom center/100% 153px;
    padding: 40px 8px;

    &-title {
      padding: 0 22px;
    }
  }

  &__over {
    height: 285px;
    margin-top: 20px;
    overflow-y: scroll;
    line-height: 28px;
    padding: 0 22px;
  }

  &__scroll {
    width: 121px;
    height: 32px;
    line-height: 32px;
    margin: 28px auto 0 auto;
    border-radius: 16px;
  }
}
</style>
