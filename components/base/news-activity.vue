<template>
  <div class="news-activity__container flex--row">
    <a
      class="news-activity__aside border-radius"
      :href="top.url"
      target="_blank"
      :title="top.title"
      rel="nofollow"
    >
      <img
        class="news-activity__aside--cover image--cover"
        :src="top.coverThumb"
        :alt="top.title"
      />
      <div class="news-activity__release font-color--basic">
        {{ top.refreshDate }}发布
      </div>
      <div class="news-activity__aside-title font-weight--bold">
        {{ top.title }}
      </div>
      <div
        class="news-activity__aside-desc font-color--basic overflow--ellipsis"
      >
        {{ top.seoDescription }}
      </div>
    </a>

    <div class="news-activity__content flex--1 overflow--hidden border-radius">
      <el-carousel
        class="carousel--primary carousel__button--large"
        indicator-position="outside"
        arrow="never"
        trigger="click"
        height="375px"
        :autoplay="false"
      >
        <el-carousel-item
          v-for="group in list"
          class="activity__item flex-justify--between flex--wrap"
        >
          <a
            v-for="item in group"
            class="flex--row news-activity__item"
            :href="item.url"
            :title="item.title"
            target="_blank"
            rel="nofollow"
          >
            <img
              class="news-activity__cover border-radius image--cover"
              :src="item.coverThumb"
              :alt="item.title"
            />
            <div class="flex--1 overflow--hidden">
              <div
                class="news-activity__title font-weight--bold font-size--16 overflow--ellipsis"
              >
                {{ item.title }}
              </div>
              <div class="news-activity__desc font-color--basic">
                {{ item.seoDescription }}
              </div>
              <div class="news-activity__release font-color--basic">
                {{ item.refreshDate }}
              </div>
            </div>
          </a>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object as Components.BaseNewsActivityProp,
    default: {
      topNews: {},
      pageList: [],
    },
  },
})

const top = computed(() => props.data.topNews)
const list = computed(() => props.data.pageList)
</script>

<style scoped lang="scss">
.news-activity {
  &__aside {
    background-color: var(--color-white);
    width: 450px;
    padding: 20px 20px 28px;
    margin-right: 20px;
  }

  &__aside--cover {
    width: 100%;
    height: 223px;
    display: block;
    margin-bottom: 16px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  &__release {
    padding-left: 16px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/clock-default.png)
      no-repeat left/12px 12px;
  }

  &__aside-title {
    @include ellipsis-lines(2, 24px, 16px);
    margin-top: 11px;
    margin-bottom: 9px;
  }

  &__content {
    background-color: var(--color-white);
    padding-bottom: 12px;
  }

  &__item {
    padding: 14px 20px 15px;
    position: relative;

    & ~ .news-activity__item {
      &::before {
        display: block;
        content: '';
        position: absolute;
        left: 20px;
        top: 0;
        right: 20px;
        border-top: 1px dashed var(--color-border);
      }
    }
  }

  &__cover {
    width: 170px;
    height: 93px;
    margin-right: 20px;
  }

  &__desc {
    @include ellipsis-lines(2, 21px, 14px);
    margin-top: 8px;
    margin-bottom: 4px;
  }
}

:deep() {
  .el-carousel__indicators {
    display: flex;
    justify-content: center;
  }

  .el-carousel__indicator {
    padding: 3px 4px 0;
  }
}
</style>
