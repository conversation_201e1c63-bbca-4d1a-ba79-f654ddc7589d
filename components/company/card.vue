<template>
  <div
    class="card__container border-radius--16 animation-mouseover--border background-color--default"
  >
    <a
      :href="data.companyUrl"
      target="_blank"
      class="card__header display--row linear-gradient--card flex--row"
      @click="handleShowCase()"
    >
      <img
        v-if="data.isShowcase === '1'"
        src="assets/images/icons/zan.png"
        class="image--zan image--contain"
      />

      <img
        :src="data.logoUrl"
        :alt="data.name"
        class="image--logo image--contain"
      />

      <div class="card__information flex--column flex-justify--between">
        <div
          class="font-size--16 overflow--ellipsis font-weight--bold"
          :title="data.name"
        >
          {{ data.name }}
        </div>

        <div class="font-color--basic overflow--ellipsis" title="">
          {{ data.companyInfo }}
        </div>
      </div>
    </a>

    <div class="card__info">
      <div class="card__content" v-if="noticeResult">
        <div
          class="card__item flex--row flex-justify--between"
          v-for="item in data.announcementList"
          :key="item.id"
        >
          <a
            :href="item.announcementUrl"
            target="_blank"
            class="card__notice display--block overflow--ellipsis position--relative"
            :title="item.title"
            @click="handleShowCase()"
            >{{ item.title }}</a
          >

          <div class="font-color--label">{{ item.refreshDate }}</div>
        </div>
      </div>

      <div class="card__empty flex--column flex-justify--center" v-else>
        <div class="font-color--label font-size--12">更多招聘信息请前往</div>
        <div class="font-color--label font-size--12">单位中心查看</div>
      </div>

      <a
        class="card__bottom flex--row flex-justify--between flex-align--center"
        :href="data.companyUrl"
        target="_blank"
      >
        <div class="card__bottom__left flex--row">
          <div>
            <span class="color--default">在招公告：</span>
            <span class="color--primary font-weight--bold">{{
              data.onlineAnnouncementAmount
            }}</span>
          </div>

          <div class="card__bottom__notice">
            <span class="color--default">在招职位：</span>
            <span class="color--primary font-weight--bold">{{
              data.onlineJobAmount
            }}</span>
          </div>
        </div>

        <div class="card__bottom__right">
          <div class="card__location overflow--ellipsis font-color--basic">
            {{ data.address }}
          </div>

          <el-button
            class="card__button linear-gradient--primary font-weight--bold"
            round
            @click="handleShowCase()"
            >查看更多</el-button
          >
        </div>
      </a>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object as Components.CompanyCardPropData,
    default: () => {},
  },
})

const handleShowCase = () => {
  let data = props.data;
  if (data.isShowcase !== '1') return; //如果不是广告位，中断
  addShowcase(data.showcaseId, data.showcaseNumber)
}

const noticeResult = computed(() => props.data.announcementList?.length)
</script>

<style lang="scss" scoped>
.card {
  &__container {
    width: 386px;
    height: 225px;
    margin-bottom: 22px;
    margin-right: 20px;

    &:nth-of-type(3n) {
      margin-right: 0;
    }

    img {
      width: 50px;
      height: 50px;
    }

    &:hover {
      .card__location {
        display: none;
      }

      .card__button {
        display: inline-flex;
      }
    }
  }

  &__header {
    border: 2px solid var(--color-white);
    border-bottom: none;
    border-radius: 16px 16px 0 0;
    padding: 15px 18px;
    height: 80px;
    position: relative;

    .image--zan {
      position: absolute;
      right: 0;
      top: 0;
      width: 26px;
      height: 22px;
    }
  }

  &__info {
    padding: 0 20px;
  }

  &__information {
    margin-left: 12px;
    width: 275px;
  }

  &__content {
    padding-top: 18px;
    height: 88px;
  }

  &__item {
    padding-bottom: 18px;
  }

  &__notice {
    @include primary-point(10);
  }

  &__bottom {
    height: 54px;
    border-top: 1px dashed var(--color-border);

    &__notice {
      margin-left: 16px;
    }
  }

  &__location {
    max-width: 90px;
    padding-left: 20px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/address-default.png')
      no-repeat left center / 13px;
  }

  &__button {
    display: none;
    color: var(--color-white);
  }

  &__empty {
    height: 88px;
    padding-left: 168px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/empty.png')
      no-repeat left 70px center / 90px 66px;
  }
}
</style>
