<template>
  <div class="filter__container border-radius background-color--default">
    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          所在地区
        </div>

        <div
          class="filter__value flex--1"
          :class="showMoreArea ? 'filter__complete' : ''"
        >
          <el-tag
            type="info"
            class="cursor--pointer"
            :class="calcAreaAllClassName"
            @click="() => handleCheck('area', '')"
          >
            全部
          </el-tag>

          <el-tag
            v-for="item in areaList"
            :key="item.k"
            type="info"
            class="cursor--pointer"
            :class="
              checkedList.area.includes(item.k as never) ? 'is-active' : ''
            "
            @click="() => handleCheck('area', item.k)"
          >
            {{ item.v }}
          </el-tag>
        </div>
      </div>
      <div
        class="filter__event cursor--pointer"
        :class="showMoreArea ? '' : 'filter__more'"
        @click="handleShowMore"
      >
        更多
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between filter__type">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          单位类型
        </div>

        <div class="flex--1">
          <el-tag
            type="info"
            class="cursor--pointer"
            :class="calcTypeAllClassName"
            @click="() => handleCheck('type', '')"
          >
            全部
          </el-tag>

          <el-tag
            v-for="item in companyTypeList"
            :key="item.k"
            type="info"
            class="cursor--pointer"
            :class="
              checkedList.type.includes(item.k as never) ? 'is-active' : ''
            "
            @click="() => handleCheck('type', item.k)"
          >
            {{ item.v }}
          </el-tag>
        </div>
      </div>
      <div
        class="filter__reset flex--row flex-align--flex-end font-color--basic cursor--pointer"
        @click="handleResearch"
      >
        清空筛选条件
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { Reactive } from 'vue'

interface CheckedList {
  area: []
  type: []
}

defineProps({
  areaList: {
    type: Array as Components.CompanyAreaData,
    default: () => {},
  },

  companyTypeList: {
    type: Array as Components.CompanyTypeData,
    default: () => {},
  },
})

const emits = defineEmits(['update:params'])

const showMoreArea = ref(false)

const checkedList = <Reactive<CheckedList>>reactive({
  area: [],
  type: [],
})

const calcAreaAllClassName = computed(() =>
  checkedList.area.length ? '' : 'is-default',
)

const calcTypeAllClassName = computed(() =>
  checkedList.type.length ? '' : 'is-default',
)

const handleShowMore = () => {
  showMoreArea.value = !showMoreArea.value
}

const updateParams = () => {
  emits('update:params', { ...checkedList, page: 1 })
}

const handleCheck = (key: keyof CheckedList, value: string) => {
  const current = checkedList[key]

  if (value === '') {
    if (current.length === 0) return

    checkedList[key] = []

    updateParams()
    return
  }

  const val = value as never
  const exist = current.includes(val)

  if (exist) {
    checkedList[key] = current.filter((item) => item !== val) as never
  } else {
    if (current.length >= 5) {
      return ElMessage.warning('最多可选择5项')
    }

    checkedList[key].push(val)
  }

  updateParams()
}

const handleResearch = () => {
  Object.keys(checkedList).forEach((key) => {
    checkedList[key as keyof CheckedList] = [] as never
  })

  updateParams()
}
</script>

<style lang="scss" scoped>
.filter {
  &__container {
    padding: 23px 20px;

    .el-tag--info {
      background-color: transparent;
      padding: 7px 10px;
      border: none;
      font-size: var(--font-size);
      color: $font-color-default;
      margin-bottom: 6px;
      margin-right: 6px;

      &:hover {
        color: $color-primary;
      }
    }

    .el-tag--primary {
      font-size: 14px;
      margin-bottom: 6px;
      margin-right: 6px;
    }

    .is-default,
    .is-active {
      color: var(--color-primary);
      background-color: #e5edff;
    }

    .is-active {
      padding-right: 20px;
      background: #e5edff
        url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/delete-primary.png)
        no-repeat right 5px center / 12px;
    }
  }

  &__label {
    margin-right: 20px;
    margin-top: 2px;
  }

  &__event {
    padding-right: 20px;
    margin: 2px 0 0 10px;
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/show-more.png');
    background-repeat: no-repeat;
    background-position: top 7px right;
    background-size: 9px 5px;
  }

  &__value {
    height: 55px;
    overflow: hidden;
  }

  &__more {
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/fold.png');
  }

  &__complete {
    height: auto;
  }

  &__type {
    margin-top: 20px;
  }

  &__reset {
    padding-left: 19px;
    margin-bottom: 2px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/delete-default.png')
      no-repeat left bottom 3px / 14px;
  }
}
</style>
