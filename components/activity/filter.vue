<template>
  <div class="filter__container border-radius background-color--default">
    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动类型
        </div>

        <div class="flex--1">
          <nuxt-link
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityType"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          所在地区
        </div>

        <div
          class="filter__value flex--1"
          :class="showMoreArea ? 'filter__complete' : ''"
        >
          <nuxt-link
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.area"
            :key="item.id"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
      <div
        class="filter__event cursor--pointer"
        :class="showMoreArea ? 'filter__more' : ''"
        @click="handleShowMore"
      >
        更多
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          单位类型
        </div>

        <div class="flex--1">
          <nuxt-link
            @click.prevent="search(item.url)"
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.companyCategory"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动时间
        </div>

        <div class="flex--1">
          <nuxt-link
            @click.prevent="search(item.url)"
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityTime"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >

          <el-date-picker
            size="default"
            :class="date ? 'is-selected' : ''"
            v-model="date"
            type="monthrange"
            range-separator="至"
            value-format="YYYY-MM"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动状态
        </div>

        <div class="flex--1">
          <nuxt-link
            @click.prevent="search(item.url)"
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityStatus"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
      <div
        class="filter__reset flex--row flex-align--flex-end font-color--basic cursor--pointer"
        @click="handleClear"
      >
        清空筛选条件
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object as Components.ActivityFilterProp,
    default: {
      activityStatus: [],
      activityTime: [],
      activityType: [],
      area: [],
      companyCategory: [],
    },
  },
  value: {
    type: Object,
    default: {
      date: '',
    },
  },
})

const emit = defineEmits(['change'])
const router = useRouter()
const {
  routeList: { activity },
} = useConfig()

const date = ref('')

const showMoreArea = ref(false)

const handleShowMore = () => {
  showMoreArea.value = !showMoreArea.value
}

watch(
  () => props.value.date,
  (value) => {
    date.value = value
  },
  { immediate: true },
)

const handleDateChange = () => {
  const route = useRoute()
  const {
    query: { activityTime, ...other },
  } = route
  const activityCustomTime = Array.isArray(date.value) ? date.value.join() : ''

  emit('change', { activityCustomTime, ...other })
}

const search = (url: string) => {
  const [, queryUrl = ''] = url.split('?')
  const query = {} as { [property: string]: any }
  queryUrl.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    query[key] = value
  })
  if ('activityTime' in query) {
    date.value = ''
  }
  emit('change', query)
}

const handleClear = () => {
  date.value = ''
  router.push({
    path: activity,
  })
  emit('change', {})
}
</script>

<style lang="scss" scoped>
.filter {
  &__container {
    padding: 23px 20px;
  }

  &__item {
    line-height: 1;
    display: inline-block;
    padding: 7px 10px;
    border: none;
    margin-right: 6px;
    margin-bottom: 5px;

    &.router-link-active {
      color: $font-color-default;

      &:hover {
        color: $color-primary;
      }
    }

    &.is-active {
      color: var(--color-primary);
      background-color: var(--label-background-primary);
    }
  }

  &__label {
    line-height: 28px;
    margin-right: 20px;
  }

  &__event {
    padding-right: 20px;
    margin: 2px 0 0 10px;
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/show-more.png');
    background-repeat: no-repeat;
    background-position: top 7px right;
    background-size: 9px 5px;
  }

  &__value {
    height: 66px;
    overflow: hidden;
  }

  &__more {
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/fold.png');
  }

  &__complete {
    height: auto;
  }

  &__reset {
    padding-left: 19px;
    margin-bottom: 2px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/delete-default.png')
      no-repeat left bottom 3px / 14px;
  }
}

:deep() {
  .el-date-editor {
    box-shadow: none;
    width: 224px;
    background-color: var(--background-second);
    height: 28px;

    &:hover {
      box-shadow: none;
    }

    &.is-selected {
      background-color: #e5edff;

      .el-icon,
      .el-range-input,
      .el-range-separator {
        color: var(--color-primary);
      }
    }
  }
}
</style>
