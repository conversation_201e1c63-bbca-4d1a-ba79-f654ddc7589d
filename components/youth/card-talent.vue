<template>
  <div class="talent__container border-radius box-shadow overflow--hidden">
    <div class="talent__header position--relative">
      <div class="talent__info">
        <div class="font-size--18 talent__name font-weight--bold">
          {{ data.name }}
        </div>

        <div class="font-color--basic font-size--12 mb-10">
          年龄区间：{{ data.age }}
        </div>
        <div class="font-color--basic font-size--12">
          海外经历时长：{{ data.experience }}
        </div>
      </div>

      <div class="talent__avatar position--absolute text-align--center">
        <div class="font-size--12">ID</div>
        <div class="font-size--12">{{ data.id }}</div>

        <div
          class="talent__avatar-image"
          :class="`talent__avatar-image-${data.avatarGender}`"
        ></div>
      </div>
    </div>

    <div class="talent__detail">
      <div
        class="font-size--18 text-align--center talent__school font-weight--bold"
      >
        {{ data.school }}
      </div>
      <div class="text-align--center font-size--12 font-color--basic">
        {{ data.schoolType }}
      </div>

      <div class="talent__experience">
        <div
          class="talent__experience-item flex--row flex-justify--between"
          v-for="item in data.experienceList"
        >
          <div class="font-color--basic">{{ item.title }}</div>
          <div class="talent__major">{{ item.major }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const prop = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.talent {
  &__container {
    width: 318px;
    height: 404px;
  }

  &__avatar {
    width: 70px;
    height: 98px;
    padding-top: 5px;
    top: 0;
    right: 30px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/avatar-bg.png)
      no-repeat center / cover;
    color: rgba(#fff, 0.6);

    &-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin: 0 auto;

      &-male {
        &::before {
          content: '';
          display: block;
          width: 50px;
          height: 50px;
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/talent-male.png)
            no-repeat center/cover;
        }
      }
      &-female {
        &::before {
          content: '';
          display: block;
          width: 50px;
          height: 50px;
          background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/talent-female.png)
            no-repeat center/cover;
        }
      }
    }
  }

  &__info {
    padding: 18px 15px;
  }

  &__header {
    height: 108px;
    border-radius: 12px 12px 0 0;
    background: #e1eafa
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/talent-bg.png)
      no-repeat bottom left / 234px 104px;
  }

  &__name {
    margin-bottom: 10px;
    color: #2a4686;
  }

  &__detail {
    height: 296px;
    padding: 18px 15px 20px 15px;
  }

  &__school {
    color: #2a4686;
    padding-bottom: 18px;
    border-bottom: 1px dashed var(--color-primary);
    margin-bottom: 8px;
  }

  &__experience {
    margin-top: 20px;

    &-item {
      margin-bottom: 12px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }

  &__major {
    text-align: right;
    flex: 1;
    margin-left: 30px;
  }
}

.mb-10 {
  margin-bottom: 10px;
}
</style>
