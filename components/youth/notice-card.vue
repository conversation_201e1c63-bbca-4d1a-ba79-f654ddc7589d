<template>
  <a
    :href="data.url"
    class="notice__container display--block border-radius background-color--default animation-mouseover--border box-shadow cursor--pointer"
    target="_blank"
    :rel="data.rel"
    :title="data.title"
    @click="addShowcase(data.id, data.number)"
  >
    <img :src="data.image" class="image--cover" :alt="data.title" />

    <div class="notice__bottom">
      <div
        class="font-size--16 font-weight--bold notice__title"
        :title="data.title"
      >
        {{ data.title }}
      </div>

      <div class="flex--row flex-align--center">
        <div
          class="font-color--basic flex--1 overflow--ellipsis"
          :title="data.companyName"
        >
          {{ data.companyName }}
        </div>

        <el-button
          class="notice__item-button linear-gradient--primary font-weight--bold border-radius--16 flex--row flex-align--center flex-justify--center"
          target="_blank"
          round
        >
          立即申报
        </el-button>
      </div>
    </div>
  </a>
</template>

<script lang="ts" setup>
defineProps({
  data: {
    type: Object as Components.YouthNoticeProp,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.notice {
  $alias: &;

  &__container {
    width: 285px;
    height: 272px;

    img {
      width: 285px;
      height: 150px;
      border-radius: 12px 12px 0 0;
    }
  }

  &__bottom {
    height: 122px;
    padding: 15px;
    margin-top: -6px;
  }

  &__title {
    @include ellipsis-lines(2, 24px, 16px);
    margin-bottom: 10px;
  }

  &__item {
    &-button {
      width: 96px;
      height: 32px;
      color: var(--color-white);
    }

    #{$alias}__button {
      margin-left: 20px;
      color: var(--color-white);
    }
  }
}
</style>
