<template>
  <div
    class="news__container border-radius background-color--default flex--row"
  >
    <div :class="`news__${className}`" class="news__left">
      <div
        class="news__label color--white flex--row flex-align--center flex-justify--center"
      >
        {{ title }}
      </div>
    </div>
    <el-carousel
      class="news__right flex--1 carousel--primary carousel__button--large"
      height="128px"
      :autoplay="false"
      arrow="never"
      trigger="click"
    >
      <el-carousel-item
        v-for="(group, index) in data"
        class="flex--row"
        :key="index"
      >
        <a
          v-for="item in group"
          :key="item.id"
          class="display--block news__link overflow--ellipsis"
          :href="item.url"
          :title="item.title"
          target="_blank"
          rel="nofollow"
          >{{ item.title }}</a
        >
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '申报动态',
  },

  className: {
    type: String,
    default: 'dynamic',
  },

  data: {
    type: Array as Components.YouthIntroduction,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
.news {
  &__container {
    width: 588px;
    height: 130px;
    border: 1px solid var(--color-border-default);
  }

  &__left {
    width: 120px;
    height: 100%;
    background-color: #e2ecff;
    border-radius: 12px 0 0 12px;
  }

  $iconList: dynamic, file;

  @each $icon in $iconList {
    &__#{$icon} {
      background: #e2ecff
        url(https://img.gaoxiaojob.com/uploads/haiwai/images/youth/#{$icon}.png)
        no-repeat
        top
        30px
        center /
        90px;
    }
  }

  &__label {
    width: 80px;
    height: 28px;
    background-color: var(--color-primary);
    border-radius: 12px 0 12px 0;
  }

  &__right {
    padding: 20px;
  }

  &__link {
    width: 427px;
    margin-bottom: 15px;
    line-height: 1;

    @include primary-point(7);
  }
}
</style>
