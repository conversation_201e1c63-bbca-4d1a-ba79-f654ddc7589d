<template>
  <div class="cooperation__container flex--column flex--center">
    <div class="cooperation__header flex--row flex-align--center font-size--20">
      <img
        src="https://img.gaoxiaojob.com/uploads/haiwai/images/app/logo.png"
        alt=""
        class="cooperation__logo"
      />
      <span>聚焦高端人才&nbsp;专业就选高才</span>
    </div>

    <div class="font-size--28 font-weight--bold">
      专注于提供海外高层次人才招聘解决方案
    </div>

    <div class="cooperation__footer">
      <base-link
        class="cooperation__button linear-gradient--primary"
        :href="companyCooperationUrl"
        size="large"
        width="140px"
        text="合作申请"
        rel="nofollow"
      />

      <base-link
        class="cooperation__button--phone"
        size="large"
        width="188px"
        style="padding-left: 20px"
        :text="phoneNumber"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { phoneNumber } = useConfig()

const { companyCooperationUrl } = useConfig()
</script>

<style scoped lang="scss">
.cooperation {
  &__container {
    height: 215px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/app/cooperation.png)
      no-repeat center / cover;
  }

  &__logo {
    margin-right: 8px;
    width: 40px;
    height: 31px;
  }

  &__header {
    margin-bottom: 10px;
    color: #0e247c;
  }

  &__footer {
    margin-top: 20px;
  }

  &__button {
    margin-right: 20px;
    color: var(--color-white);

    &.linear-gradient--primary {
      box-shadow: none;
    }

    &--phone {
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/icons/phone-primary.png)
        no-repeat 30px 8px / 25px;
    }
  }
}
</style>
