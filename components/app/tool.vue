<template>
  <div class="tool__container position--fixed position--y-center">
    <ul
      class="tool__list background-color--default tool--shadow border-radius--8"
    >
      <el-popover width="175px" placement="left">
        <template #reference>
          <li class="tool__item flex--row flex--center">公众号</li>
        </template>

        <div class="official__code flex--column flex--center">
          <div class="official__code-image">
            <img
              class="display--block image--contain"
              src="public/official-code.png"
              alt=""
            />
          </div>

          <div class="flex--column flex--nowrap flex-align--center">
            <span
              class="official__code-title flex--row flex--center font-color"
            >
              扫码关注
            </span>

            <span class="font-color--basic font-size--12">
              高才海外HiTalentGlobal
            </span>
          </div>
        </div>
      </el-popover>

      <el-popover width="295px" placement="left">
        <template #reference>
          <li class="tool__item flex--row flex--center">联系<br />方式</li>
        </template>

        <div class="contact__method">
          <div
            class="contact__method-title contact__method--padding font-color font-weight--bold"
          >
            联系方式
          </div>

          <ul
            class="contact__method-list contact__method--padding font-color--basic font-size--12"
          >
            <li
              v-for="(item, index) in contactList"
              class="contact__method-item"
              :key="index"
            >
              <span class="font-color font-weight--bold">{{ item.label }}</span>
              ：
              {{ item.value }}
            </li>
          </ul>
        </div>
      </el-popover>

      <li class="tool__item flex--row flex--center">
        <a
          class="text-align--center"
          :href="companyCooperationUrl"
          target="_blank"
          rel="nofollow"
        >
          合作<br />咨询
        </a>
      </li>

      <li class="tool__item flex--row flex--center">
        <a class="text-align--center" :href="domainUrl" target="_blank">
          招聘<br />官网
        </a>
      </li>
    </ul>

    <div
      v-if="showUp"
      class="tool__up tool--shadow color--primary font-weight--bold text-align--center border-radius--8 cursor--pointer"
      @click="handleUp"
    >
      TOP
    </div>
  </div>
</template>

<script setup lang="ts">
const {
  eventBus,
  domainUrl,
  companyCooperationUrl,
  qqNumber,
  phoneNumber,
  mobileNumber,
  emailAddress,
} = useConfig()

const showUp = ref(false)

const contactList = [
  { label: '电话', value: `${phoneNumber}  ${mobileNumber}` },
  { label: '微信', value: mobileNumber },
  { label: 'Q Q', value: qqNumber },
  { label: '邮箱', value: emailAddress },
]

const handleUp = () => {
  smoothScrollTo()
}

const handleShow = () => {
  showUp.value = getScrollTop() >= getWindowRect().height
}

onMounted(() => {
  handleShow()

  eventBus.on('scroll', () => {
    handleShow()
  })
})
</script>

<style lang="scss">
.official__code {
  &-image {
    width: 140px;
  }

  &-title {
    margin: 4px auto;

    &::before,
    &::after {
      content: '';
      margin: 0 4px;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: var(--color-primary);
    }
  }
}

.contact__method {
  padding: 4px 8px 8px;

  &--padding {
    padding-left: 32px;
  }

  &-title {
    margin-bottom: 6px;
    line-height: 20px;
    background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/app/contact.png)
      no-repeat 0 -2px / 20px;
  }

  &-item {
    line-height: 1.5;
  }
}
</style>

<style scoped lang="scss">
.tool {
  &__container {
    width: 56px;
    right: 40px;

    z-index: var(--z-index);
  }

  &--shadow {
    box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.2);
  }

  &__item {
    height: 60px;
    border-bottom: 1px solid var(--color-border);

    &:last-of-type {
      border-bottom: none;
    }

    &:hover {
      color: var(--color-primary);
    }

    a {
      width: 100%;
    }
  }

  &__up {
    margin-top: 20px;
    padding-top: 24px;
    height: 56px;
    background: var(--color-white)
      url(https://img.gaoxiaojob.com/uploads/haiwai/images/app/up.png) no-repeat
      center 8px / 17px;
  }
}
</style>
