<template>
  <footer class="footer__container">
    <nav class="footer__nav flex--row flex--center">
      <template v-for="(nav, index) in navList">
        <a :href="nav.url" target="_blank" rel="nofollow"> {{ nav.name }} </a>
        <template v-if="index < navList.length - 1"> | </template>
      </template>
    </nav>

    <div class="footer__copyright text-align--center">
      <p>
        Copyright © 2007-{{ currentDate.year }} 高校人才网 版权所有
        网站备案信息：

        <a href="//beian.miit.gov.cn" target="_blank" rel="nofollow"
          >粤ICP备13048400号</a
        >

        粤公网安备：
        <a
          href="//www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010602004138"
          target="_blank"
          rel="nofollow"
        >
          44010602004138号
        </a>
      </p>

      <p>本站由广州高才信息科技有限公司运营</p>

      <p>
        中华人民共和国增值电信业务经营许可证：
        <a
          href="//zt.gaoxiaojob.com/zzdxywjyxkz.jpg"
          target="_blank"
          rel="nofollow"
          >粤B2-20180648</a
        >
      </p>

      <p>
        人力资源服务许可证编号：(粤)人服证字(2022) 第0106114823
        企业统一社会信用代码：91440106MA59BTXW56
      </p>

      <p>
        客户咨询电话：{{ phoneNumber }}&nbsp;QQ：{{ qqNumber }}&nbsp;邮箱：
        {{ emailAddress }}
      </p>

      <p>高校人才网——国内访问量、信息量领先的高层次人才需求信息平台</p>

      <p>本平台由广东同福律师事务所提供法律支持服务</p>
    </div>
  </footer>
</template>

<script setup lang="ts">
const { domain, domainUrl, qqNumber, phoneNumber, emailAddress } = useConfig()
const { currentDate } = useServer()

const generateUrl = (path: string) => `${domainUrl}${path}`

const navList = [
  { name: '关于我们', url: generateUrl('/zhaopin/aboutUs/index.html') },
  { name: '联系我们', url: generateUrl('/zhaopin/aboutUs/contactUs.html') },
  { name: '人才招聘', url: generateUrl('/zhaopin/aboutUs/joinUs.html') },
  {
    name: '产品服务',
    url: generateUrl('/zhaopin/aboutUs/productService.html'),
  },
  { name: '免责声明', url: generateUrl('/zhaopin/aboutUs/disclaimers.html') },
  { name: '网站导航', url: generateUrl('/data/sitemap.html') },
  {
    name: '资质证明',
    url: generateUrl('/zhaopin/zhuanti/zzzm2021/index.html'),
  },
  {
    name: '高才科技官网',
    url: `//gaocai.${domain}`,
  },
]
</script>

<style scoped lang="scss">
.footer {
  &__container {
    width: 100%;
    min-width: var(--view-width);
    color: var(--color-footer);

    a {
      margin: 0 10px;
      color: var(--color-footer);
    }
  }

  &__nav {
    height: 50px;
    background-color: var(--color-footer-nav-bg);
  }

  &__copyright {
    padding: 10px 0;
    line-height: 2;
    background-color: var(--color-footer-copyright-bg);
  }
}
</style>
