<template>
  <ul
    v-if="hasData(menuList)"
    id="menu"
    class="menu__list position--fixed background-color--default border-radius--8 box-shadow animation-transition"
    :style="menuStyle"
  >
    <li
      class="menu__item display--block position--relative text-align--center cursor--pointer"
      v-for="item in menuList"
      :key="item.name"
      :class="item.name === current?.name ? 'menu__item--active' : ''"
      @click="() => handleActive(item)"
    >
      {{ item.name }}
    </li>
  </ul>
</template>

<script setup lang="ts">
const props = defineProps({
  data: {
    type: Array as Components.BaseMenuPropData,
    default: () => [],
  },
})

const { eventBus } = useConfig()

const current = ref()

const menuList = computed(() =>
  props.data.filter((item: Base.Menu) => item.visible),
)

const menuStyle = ref({})

const getTargetName = (data: Base.Menu) => data.value ?? data.name

const handleActive = (data: Base.Menu) => {
  scrollToTarget(getTargetName(data))
}

const handleScroll = () => {
  const { width, height } = getWindowRect()

  const menuDom = document.querySelector('#menu') as HTMLDivElement
  const viewDom = document.querySelector('.view--width') as HTMLDivElement

  const items = unref(menuList)

  if (menuDom && viewDom) {
    const { offsetWidth, offsetHeight } = menuDom
    const { offsetLeft } = viewDom

    const top =
      getScrollTop() >= offsetHeight ? `${(height - offsetHeight) / 2}px` : null

    // https://zentao.jugaocai.com/index.php?m=story&f=view&id=885
    let left = '0px'
    if (width > viewDom.offsetWidth && width <= 1440) {
      left = `${offsetLeft - offsetWidth - 3}px`
    }

    if (width > 1440) {
      left = `${offsetLeft - offsetWidth - 40}px`
    }

    const display = width < viewDom.offsetWidth ? 'none' : 'block'

    menuStyle.value = { top, left, display }
  }

  for (let index = 0; index < items.length; index++) {
    const dom = document.querySelector(`#${getTargetName(items[index])}`)

    if (!dom) return

    const { top } = dom.getBoundingClientRect()

    if (top >= 0 && top <= height) {
      current.value = items[index]
      break
    }
  }
}

onMounted(() => {
  handleScroll()

  eventBus.on('scroll', () => {
    handleScroll()
  })

  eventBus.on('resize', () => {
    handleScroll()
  })
})
</script>

<style scoped lang="scss">
.menu {
  &__list {
    top: 550px;
    left: 40px;
    z-index: var(--z-index);

    &::before {
      content: '';
      position: absolute;
      top: -35px;
      left: 50%;
      width: 80px;
      height: 35px;
      background: url(https://img.gaoxiaojob.com/uploads/haiwai/images/app/menu.png)
        no-repeat center / contain;
      transform: translateX(-50%);
    }
  }

  &__item {
    padding: 0 15px;
    line-height: 36px;
    border-bottom: 1px solid var(--color-border);

    &--active {
      color: var(--color-primary);
      font-weight: bold;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: calc(16 / 36) * 100%;
        background-color: var(--color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }

    &:last-of-type {
      border-bottom: none;
    }
  }
}
</style>
