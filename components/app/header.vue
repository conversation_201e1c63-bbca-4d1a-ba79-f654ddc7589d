<template>
  <header
    id="header"
    class="header__container font-size--16 background-color--default"
    :class="fixedClassName"
  >
    <div class="header__main view--width flex--row flex--center">
      <nuxt-link class="header__logo flex--none" :to="home">
        <img
          class="image--contain"
          src="public/logo.png"
          alt="高才海外HiTalentGlobal"
        />
      </nuxt-link>

      <nav class="header__nav">
        <!--        <nuxt-link-->
        <!--          v-for="nav in navList"-->
        <!--          :class="matchRouteActive(nav.path)"-->
        <!--          :key="nav.path"-->
        <!--          :to="nav.path"-->
        <!--        >-->
        <!--          {{ nav.name }}-->
        <!--        </nuxt-link>-->
        <!-- 改成a连接 -->
        <a
          v-for="nav in navList"
          :href="nav.path"
          :class="matchRouteActive(nav.path)"
          >{{ nav.name }}</a
        >
      </nav>

      <div
        class="header__aside flex--row flex-justify--flex-end flex-align--center"
      >
        <el-dropdown>
          <nuxt-link
            class="header__about aside__link font-size--16"
            :to="about"
          >
            全球合作
          </nuxt-link>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in aboutList" :key="item.url">
                <a :href="item.url" :target="item.target">
                  {{ item.name }}
                </a>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <a class="aside__link" :href="urlBase" target="_blank">招聘官网</a>

        <a
          class="flex--row"
          v-if="userData.avatar"
          :href="personUrl"
          target="_blank"
        >
          <el-avatar :src="userData.avatar" :size="36" />
        </a>

        <a
          v-else
          class="header__login linear-gradient--primary color--white font-size--16 font-weight--bold border-radius--16"
          :href="personLoginUrl"
          target="_blank"
          rel="nofollow"
        >
          人才登录
        </a>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
const props = defineProps({
  fixed: {
    type: Boolean,
    default: false,
  },
})

const { routeList, urlBase, personUrl, personLoginUrl, companyCooperationUrl } =
  useConfig()

const { currentDate } = useServer()

const route = useRoute()

const {
  home,
  talent,
  activity,
  announcement,
  youth,
  company,
  about,
  aboutService,
  aboutAdvantage,
  aboutCase,
} = routeList

const userData = reactive({
  avatar: '',
})

const fixedClassName = computed(() =>
  props.fixed ? 'header__container--fixed position--fixed' : '',
)

const navList = [
  { name: '首页', path: home },
  { name: '出海引才', path: talent },
  { name: '归国活动', path: activity },
  { name: '求贤公告', path: announcement },
  { name: `海外优青${currentDate.youth}`, path: youth },
  { name: '单位大厅', path: company },
]

const aboutList = [
  { name: '关于我们', url: about, target: '_self' },
  { name: '产品服务', url: aboutService, target: '_self' },
  { name: '服务优势', url: aboutAdvantage, target: '_self' },
  { name: '合作案例', url: aboutCase, target: '_self' },
  { name: '合作申请', url: companyCooperationUrl, target: '_blank' },
]

const matchRouteActive = (path: string) => {
  if (path === '/' && route.path === '/') {
    return 'router-link-active'
  }
  return !isIndexPage(path) && route.path.startsWith(path)
    ? 'router-link-active'
    : ''
}

const fetchUserAuthorization = async () => {
  const { avatar } = (await request({ url: '/api/auth' })) as API.AuthData

  userData.avatar = avatar
}

watch(
  () => route.path,
  () => {
    fetchUserAuthorization()
  },
  { immediate: true },
)
</script>

<style scoped lang="scss">
.header {
  &__container {
    width: 100%;
    height: var(--header-height);

    &--fixed {
      top: 0;
      box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.1);
      z-index: var(--z-index);
    }
  }

  &__main {
    height: 100%;
  }

  &__logo {
    margin-right: 40px;
    width: 132px;
    height: 54px;
  }

  &__nav {
    a {
      position: relative;
      margin-right: 30px;

      &:hover {
        font-weight: var(--font-weight);
      }

      &.router-link-active {
        font-weight: var(--font-weight);

        &::after {
          content: '';
          position: absolute;
          top: 30px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: var(--color-primary);
        }
      }

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  &__aside {
    flex: 1 0 auto;

    .aside__link {
      margin-right: 30px;
    }
  }

  &__about {
    padding-left: 22px;
    line-height: 22px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/global-default.png')
      no-repeat left center / 18px;

    &:hover,
    &.router-link-active {
      background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/global-active.png');
    }
  }

  &__login {
    padding: 5px 20px;

    &:hover {
      color: var(--color-white);
    }
  }
}
</style>
