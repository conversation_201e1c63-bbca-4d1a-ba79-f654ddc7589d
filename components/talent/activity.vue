<template>
  <el-carousel
    class="activity__container carousel--primary carousel__button--large"
    indicator-position="outside"
    arrow="never"
    height="414px"
    trigger="click"
    :autoplay="false"
  >
    <el-carousel-item
      v-for="list in data"
      class="activity__item flex-justify--between flex--wrap"
    >
      <base-more-activity :data="item" v-for="item in list" :type="type" />
    </el-carousel-item>
  </el-carousel>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String as Components.BaseGlobalActivityType,
    default: 'native',
  },
  data: {
    type: Array<Activity.MoreActivityList[]>,
    default: [],
  },
})
</script>

<style scoped lang="scss">
.activity {
  &__container {
    padding: 6px 2px 0;
    margin: 0 -2px;

    .more-activity__container {
      display: flex;
      margin-bottom: 20px;
    }
  }

  &__item {
    display: flex;
    align-content: start;
    overflow: visible;
  }
}

:deep() {
  .el-carousel__indicators {
    margin-top: 8px;
  }
}
</style>
