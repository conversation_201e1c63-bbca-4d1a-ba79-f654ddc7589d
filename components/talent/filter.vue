<template>
  <div class="filter__container border-radius background-color--default">
    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动类型
        </div>

        <div class="flex--1">
          <nuxt-link
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityType"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
    </div>

    <div
      class="filter__search flex--row flex-justify--between filter__search--area"
    >
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          热门地区
        </div>

        <div class="filter__value flex--1">
          <nuxt-link
            :to="item.url"
            class="filter__item font-color border-radius--4 position--relative"
            :class="{
              'is-active': item.active && !item.activeDot,
              'is-dot': item.activeDot,
            }"
            v-for="item in data.area"
            :key="item.id"
            >{{ item.name }}</nuxt-link
          >

          <template v-for="item in data.area">
            <div
              v-show="item.active && item.children"
              class="filter__item--children border-radius--4"
            >
              <nuxt-link
                :to="i.url"
                v-for="i in item.children"
                :key="i.id"
                class="filter__item font-color border-radius--4"
                :class="i.active ? 'is-active' : ''"
                >{{ i.name }}</nuxt-link
              >
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动时间
        </div>

        <div class="flex--1">
          <nuxt-link
            @click.prevent="search(item.url)"
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityTime"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >

          <el-date-picker
            size="default"
            :class="date ? 'is-selected' : ''"
            v-model="date"
            type="monthrange"
            range-separator="至"
            value-format="YYYY-MM"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>

    <div class="filter__search flex--row flex-justify--between">
      <div class="filter__cell flex--row flex-justify--between flex--1">
        <div class="filter__label font-size--16 font-weight--bold">
          活动状态
        </div>

        <div class="flex--1">
          <nuxt-link
            @click.prevent="search(item.url)"
            :to="item.url"
            class="filter__item font-color border-radius--4"
            :class="item.active ? 'is-active' : ''"
            v-for="item in data.activityStatus"
            :key="item.id"
            type="info"
            >{{ item.name }}</nuxt-link
          >
        </div>
      </div>
      <div
        class="filter__reset flex--row flex-align--flex-end font-color--basic cursor--pointer"
        @click="handleClear"
      >
        清空筛选条件
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object as Components.TalentSearchParamsProp,
    default: {
      activityStatus: [],
      activityTime: [],
      activityType: [],
      area: [],
    },
  },

  value: {
    type: Object,
    default: {
      date: '',
    },
  },
})

const emit = defineEmits(['change'])
const router = useRouter()
const {
  routeList: { talent },
} = useConfig()

const date = ref('')

watch(
  () => props.value.date,
  (value) => {
    date.value = value
  },
  { immediate: true },
)

const handleDateChange = () => {
  const route = useRoute()
  const {
    query: { activityTime, ...other },
  } = route
  const activityCustomTime = Array.isArray(date.value) ? date.value.join() : ''

  emit('change', { activityCustomTime, ...other })
}

const search = (url: string) => {
  const [, queryUrl = ''] = url.split('?')
  const query = {} as { [property: string]: any }
  queryUrl.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    query[key] = value
  })
  if ('activityTime' in query) {
    date.value = ''
  }
  emit('change', query)
}

const handleClear = () => {
  date.value = ''
  router.push({
    path: talent,
  })
  emit('change', {})
}
</script>

<style lang="scss" scoped>
.filter {
  &__container {
    padding: 23px 20px;
  }

  &__search--area {
    padding-right: 80px;
  }

  &__item {
    line-height: 1;
    display: inline-block;
    padding: 7px 10px;
    border: none;
    margin-right: 6px;
    margin-bottom: 5px;

    &--children {
      background: #f8f8f8;
      padding: 4px 10px 0;
    }

    &.router-link-active {
      color: $font-color-default;

      &:hover {
        color: $color-primary;
      }
    }

    &.is-active {
      color: var(--color-primary);
      background-color: var(--label-background-primary);
    }

    &.is-dot {
      color: var(--color-primary);

      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 4px;
        position: absolute;
        left: 1px;
        top: 12px;
        border-radius: 50%;
        background-color: var(--color-primary);
      }
    }
  }

  &__label {
    line-height: 28px;
    margin-right: 20px;
  }

  &__event {
    padding-right: 20px;
    margin: 2px 0 0 10px;
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/show-more.png');
    background-repeat: no-repeat;
    background-position: top 7px right;
    background-size: 9px 5px;
  }

  &__value {
    overflow: hidden;
  }

  &__more {
    background-image: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/fold.png');
  }

  &__complete {
    height: auto;
  }

  &__item--children {
    margin-bottom: 20px;
  }

  &__reset {
    padding-left: 19px;
    margin-bottom: 2px;
    background: url('https://img.gaoxiaojob.com/uploads/haiwai/images/icons/delete-default.png')
      no-repeat left bottom 3px / 14px;
  }
}

:deep() {
  .el-date-editor {
    box-shadow: none;
    width: 224px;
    background-color: var(--background-second);
    height: 28px;

    &:hover {
      box-shadow: none;
    }

    &.is-selected {
      background-color: #e5edff;

      .el-icon,
      .el-range-input,
      .el-range-separator {
        color: var(--color-primary);
      }
    }
  }
}
</style>
