<template>
  <div id="layout" :style="layoutStyle">
    <app-header :fixed="fixed" />

    <main id="main" class="main__container flex-column">
      <div class="main__content">
        <el-config-provider :locale="zhCn">
          <slot />
        </el-config-provider>
      </div>

      <template v-if="showCooperationComponent">
        <app-cooperation />
      </template>

      <app-footer />

      <app-tool />
    </main>
  </div>
</template>

<script setup lang="ts">
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { useCookie } from '#app'

const { eventBus } = useConfig()

const route = useRoute()

const fixed = ref(false)

const showCooperationComponent = ref(false)

const layoutStyle = computed(() =>
  unref(fixed) ? 'padding-top: var(--header-height);' : '',
)

const handleFixedValue = () => {
  fixed.value = getScrollTop() > 0
}

watch(
  () => route.path,
  () => {
    const { showCooperation } = route.meta

    showCooperationComponent.value = !!showCooperation
  },
  { immediate: true },
)

onMounted(async () => {
  handleFixedValue()

  window.addEventListener('scroll', () => {
    handleFixedValue()

    eventBus.emit('scroll')
  })

  window.addEventListener('resize', () => {
    eventBus.emit('resize')
  })

  let token = useCookie('gaoxiaojobPositionToken').value

  if (!token) {
    // 直接请求接口
    const res = (await request({
      url: '/api/common/showcase-token',
    })) as API.ShowcaseToken
    token = res.token
    // 加到现在的cookie里面
    useCookie('gaoxiaojobPositionToken').value = token
  }
})
</script>

<style scoped lang="scss"></style>
