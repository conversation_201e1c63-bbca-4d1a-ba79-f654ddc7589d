{"editor.defaultFormatter": "esbenp.prettier-vscode", "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {".editorconfig": ".g<PERSON><PERSON><PERSON>, .editorconfig, .prettierrc", "develop.d.ts": "*.d.ts", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, bun.lockb"}, "vue.server.hybridMode": false, "cSpell.words": ["chuhai", "cparagraph", "danwei", "gaocai", "gao<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gcjob", "gonggao", "gui<PERSON><PERSON>", "haiwaiyouqing", "jugaocai", "lightspot", "mockjs", "monthrange", "nofollow", "<PERSON><PERSON><PERSON>", "unhead", "unref", "<PERSON><PERSON><PERSON>", "zhuanti"]}