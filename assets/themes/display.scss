.flex {
  $direction-list: 'row', 'column';

  @each $direction in $direction-list {
    &--#{$direction} {
      display: flex;
      flex-direction: #{$direction};
    }
  }

  &--none {
    flex: none;
  }

  $wrap-list: 'wrap', 'nowrap';

  @each $wrap in $wrap-list {
    &--#{$wrap} {
      flex-wrap: #{$wrap};
    }
  }

  @for $num from 1 to 5 {
    &--#{$num} {
      flex: $num;
      flex-shrink: 0;
    }
  }

  &--center {
    justify-content: center;
    align-items: center;
  }

  $flex-justify-list: 'around', 'between', 'evenly', 'center', 'flex-start',
    'flex-end';

  @each $justify in $flex-justify-list {
    &-justify--#{$justify} {
      $value: if(
        $justify ==
          'center' or
          $justify ==
          'flex-start' or
          $justify ==
          'flex-end',
        $justify,
        space-#{$justify}
      );

      justify-content: #{$value};
    }
  }

  $flex-align-list: 'center', 'flex-start', 'flex-end';

  @each $align in $flex-align-list {
    &-align--#{$align} {
      align-items: #{$align};
    }
  }
}

$display-list: 'none', 'block', 'inline', 'inline-block';

@each $display in $display-list {
  .display--#{$display} {
    display: #{$display};
  }
}
