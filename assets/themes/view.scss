.view {
  &--width {
    margin: 0 auto;
    width: var(--view-width);
  }
}

.image {
  $image-list: 'contain', 'cover';

  @each $image in $image-list {
    &--#{$image} {
      width: 100%;
      height: 100%;
      object-fit: #{$image};
    }
  }

  &--logo {
    background-color: var(--color-white);
    border-radius: 50%;
  }
}

.scrollbar--primary {
  &::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #aac0f6;
  }
}

.carousel--primary {
  .el-carousel__indicator {
    &.is-active button {
      background-color: var(--color-primary);
    }
  }

  .el-carousel__button {
    background-color: #c3d1f0;
    opacity: 1;
  }
}

.el-carousel {
  $carousel__button--size: (
    large: (
      27px,
      4px,
    ),
  );

  @each $buttonSize in map-keys($map: $carousel__button--size) {
    &.carousel__button--#{$buttonSize} {
      $width: nth(
        $list: map-get($carousel__button--size, $buttonSize),
        $n: 1,
      );
      $height: nth(
        $list: map-get($carousel__button--size, $buttonSize),
        $n: 2,
      );

      .el-carousel__button {
        width: $width;
        height: $height;
        border-radius: $width;
      }
    }
  }
}

.el-button {
  span {
    font-weight: 700;
  }
}

.el-pagination {
  button,
  .el-pager {
    --el-pagination-button-bg-color: var(--color-white);
  }

  .btn-prev {
    &::after {
      content: 'prev';
    }
  }

  .btn-next {
    &::before {
      content: 'next';
    }
  }
}
