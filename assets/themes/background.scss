$background-color-list: (
    'primary': var(--background-primary),
  ),
  (
    'default': var(--color-white),
  );

@each $background in $background-color-list {
  $key: nth(map-keys($background), 1);
  $value: nth(map-values($background), 1);

  .background-color--#{$key} {
    background-color: $value;
  }
}

.linear-gradient {
  &--primary {
    background: linear-gradient(
      90deg,
      var(--color-primary),
      var(--color-second)
    );
  }

  &--card {
    background: linear-gradient(90deg, #e6eeff, #f5fdff);
  }
}
