@mixin ellipsis-lines(
  $line-size,
  $line-height,
  $font-size: 14px,
  $auto: false
) {
  @if (unitless($font-size)) {
    @error '$font-size must be a unit number';
  }

  $attr-height: if($auto, 'max-height', 'height');

  $line-height: if(
    unitless($line-height),
    $font-size * $line-height,
    $line-height
  );
  $max-height: $line-height * $line-size;

  display: -webkit-box;
  -webkit-line-clamp: $line-size;
  -webkit-box-orient: vertical;
  #{$attr-height}: $max-height;
  font-size: $font-size;
  line-height: $line-height;
  word-break: break-word;
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin primary-point($height) {
  position: relative;
  padding-left: 12px;

  &::before {
    content: '';
    position: absolute;
    display: inline-block;
    flex: none;
    top: #{$height}px;
    left: 0;
    width: 5px;
    height: 5px;
    background-color: var(--color-primary);
    border-radius: 50%;
    transform: translateY(-50%);
  }
}
