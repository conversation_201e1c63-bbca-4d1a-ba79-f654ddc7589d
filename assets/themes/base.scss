@use 'variables' as *;

:root {
  --view-width: 1200px;

  --header-height: 80px;

  --color-primary: #{$color-primary};

  --color-second: #29a7ff;

  --color-default: #{$font-color-default};

  --color-white: #ffffff;

  --color-highlight: #9f6d1a;

  --color-border: #ebebeb;

  --color-border-default: #d4e1ff;

  --color-footer: #8f8f8f;

  --color-footer-nav-bg: #4e4e4e;

  --color-footer-copyright-bg: #303030;

  --button-disabled-primary: #bababa;

  --background-primary: #f8faff;

  --background-second: #f8f8f8;

  --label-background-primary: #e5edff;

  --tag-background-primary: #f3f8fd;

  --font-size: 14px;

  --font-weight: bold;

  --z-index: 2000;
}

:focus-visible {
  outline: none;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.45);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  color: var(--color-default);
  font-size: var(--font-size);
  min-width: var(--view-width);
}

a {
  color: var(--color-default);
  text-decoration: none;

  &:hover,
  &.router-link-active {
    color: var(--color-primary);
  }
}

ul {
  list-style: none;
}
