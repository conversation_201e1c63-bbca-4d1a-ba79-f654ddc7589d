@use 'variables' as *;

$font-color-list: (
    'default': 1,
  ),
  (
    'basic': 0.8,
  ),
  (
    'label': 0.6,
  );

@each $color in $font-color-list {
  $key: nth(map-keys($color), 1);
  $alpha: nth(map-values($color), 1);

  $condition: $key != 'default';

  $name: if($condition, --#{$key}, '');
  $value: if(
    $condition,
    rgba($font-color-default, $alpha),
    var(--color-default)
  );

  .font-color#{$name} {
    color: $value;
  }
}

$font-size-list: 12, 13, 14, 15, 16, 18, 20, 22, 24, 28, 30, 32, 34, 36;

@each $size in $font-size-list {
  .font-size--#{$size} {
    font-size: #{$size}px;
  }
}

.font-weight--bold {
  font-weight: var(--font-weight);
}

$text-align-list: 'left', 'right', 'center';

@each $align in $text-align-list {
  .text-align--#{$align} {
    text-align: #{$align};
  }
}
