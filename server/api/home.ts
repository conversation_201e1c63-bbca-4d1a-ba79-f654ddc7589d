import Mock from 'mockjs'
import { mockImage } from '../utils/utils'

const data = Mock.mock({
  'banner|5': [
    {
      'id|+1': 1,
      image: mockImage(2560, 380),
      url: Mock.Random.url(),
    },
  ],

  'recommendedActivities|5': [
    {
      'id|+1': 1,
      url: Mock.Random.url(),
      name: Mock.Random.cparagraph(),
      image: mockImage(680, 360),
      logo: mockImage(),
      type: Mock.Random.string('12', 1, 1),
      tag: '归国活动',
      title: Mock.Random.cparagraph(),
      description: Mock.Random.cparagraph(),
      date: Mock.Random.date(),
      address: '地点：' + Mock.Random.county(true),
      rel: 'nofollow',
    },
  ],

  'overseasTalentAttractionActivities|6': [
    {
      'id|+1': 1,
      type: '1',
      tag: '专场招聘',
      image: mockImage(354, 187),
      logo: mockImage(),
      title: Mock.Random.cparagraph(),
      date: Mock.Random.date(),
      address: Mock.Random.county(true),
      btnText: '立即报名',
      'btnDisabled|1-2': true,
      url: Mock.Random.url(),
      rel: 'nofollow',
    },
  ],

  comeBackActivities: {
    'topList|2': [
      {
        'id|+1': 1,
        address: Mock.Random.county(true),
        date: Mock.Random.date(),
        title: Mock.Random.cparagraph(),
        type: '1',
        tag: '学子归国行',
        image: mockImage(590, 272),
        btnText: '立即报名',
        'btnDisabled|1-2': true,
        url: Mock.Random.url(),
        rel: 'nofollow',
      },
    ],

    'bottomList|4': [
      {
        'id|+1': 1,
        address: Mock.Random.county(true),
        date: Mock.Random.date(),
        title: Mock.Random.cparagraph(),
        type: '1',
        tag: '学者论坛',
        image: mockImage(285, 150),
        btnText: '立即报名',
        'btnDisabled|1-2': true,
        url: Mock.Random.url(),
        rel: 'nofollow',
      },
    ],
  },

  'overseasExcellentYouth|9': [
    {
      'id|+1': 1,
      title: Mock.Random.cparagraph(),
      date: Mock.Random.date(),
      address: Mock.Random.county(true),
      highlightsDescribe: '',
      companyName: Mock.Random.cparagraph(),
      companyLogo: mockImage(),
      btnText: '立即查看',
      url: Mock.Random.url(),
    },
  ],

  'renownedEmployer|9': [
    {
      'id|+1': 1,
      companyName: Mock.Random.cparagraph(),
      companyLogo: mockImage(),
      companyInfo: Mock.Random.cparagraph(),
      description: Mock.Random.cparagraph(),
      image: mockImage(554, 351),
      url: Mock.Random.url(),
    },
  ],

  'globalAnnouncement|8': [
    {
      'id|+1': 1,
      refreshDate: '05.10',
      refreshYear: '2024',
      title: Mock.Random.cparagraph(),
      recruitAmount: '1',
      jobAmount: '1',
      address: Mock.Random.county(true),
      highlightsDescribe: '',
      tag: '推荐',
      url: Mock.Random.url(),
    },
  ],

  'discoveryPosition|9': [
    {
      'id|+1': 1,
      jobName: Mock.Random.cparagraph(),
      wage: '10-25万/年',
      address: Mock.Random.county(true),
      major: '教育学原理等',
      recruitAmount: '1',
      companyName: Mock.Random.county(true),
      companyLogo: mockImage(),
      refreshDate: '05.10',
      url: Mock.Random.url(),
    },
  ],

  'collaborationCases|3': [
    {
      'id|+1': 1,
      url: Mock.Random.url(),
      logoImage: mockImage(326, 40),
      title: Mock.Random.cparagraph(),
      description: Mock.Random.cparagraph(),
      otherImage: mockImage(326, 120),
    },
  ],
})

const test = defineEventHandler(() => {
  return {
    data,
    message: '',
    status: 200,
  }
})

const home = defineRequest({ url: '/home/<USER>' })

export default home
