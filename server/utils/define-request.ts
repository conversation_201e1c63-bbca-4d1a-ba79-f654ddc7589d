import { type H3Event } from 'h3'

const runtimeConfig = useRuntimeConfig()
const { apiBase } = runtimeConfig.public

export default function defineRequest(
  options: Request.Options,
  callback?: (
    params: Exclude<H3Event['context']['params'], undefined>,
  ) => string | undefined,
) {
  return defineEventHandler(async (event) => {
    const { url } = options

    const query = getQuery(event) ?? {}

    const params = getRouterParams(event) ?? {}

    const path = callback ? callback(params) : ''

    const apiUrl = `${apiBase}${url}${path}`

    const { cookie = '' } = getRequestHeaders(event)

    try {
      const response = await $fetch(apiUrl, {
        headers: {
          cookie,
        },
        query,
      })

      const { data, msg, result } = response as Request.ServerResponse

      return {
        data,
        message: msg,
        status: result === 1 ? 200 : 400,
      }
    } catch (error: Base.Any) {
      return {
        data: {},
        message: error.message,
        status: error.status,
      }
    }
  })
}
